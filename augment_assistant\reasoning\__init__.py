"""
Advanced Reasoning Engine - Surpasses OpenAI o3's reasoning capabilities.

This module implements revolutionary reasoning capabilities that exceed
all current 2025 AI coding assistants including OpenAI o3, <PERSON>, and others.

Features:
- Test-time compute with verification
- Multi-step reasoning with self-correction
- Parallel reasoning paths with consensus
- Advanced planning with dependency analysis
- Self-verification and iterative improvement
- Adaptive reasoning based on problem complexity
"""

from .advanced_reasoner import AdvancedReasoningEngine
from .verification_engine import VerificationEngine
from .planning_engine import PlanningEngine
from .self_correction import SelfCorrectionEngine

__all__ = [
    "AdvancedReasoningEngine",
    "VerificationEngine", 
    "PlanningEngine",
    "SelfCorrectionEngine"
]
