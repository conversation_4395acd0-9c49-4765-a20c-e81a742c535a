"""
Multi-Agent Swarm Intelligence System for the Augment Coding Assistant.

This module implements a revolutionary multi-agent system where specialized AI agents
collaborate to solve complex coding problems. Each agent has specific expertise and
they work together using consensus-based decision making.

Agents:
- ArchitectAgent: System design and architecture planning
- CoderAgent: Code generation and implementation
- TesterAgent: Test generation and quality assurance
- DevOpsAgent: Deployment and infrastructure management
- SecurityAgent: Security analysis and vulnerability detection
- PerformanceAgent: Performance optimization and monitoring
- ReviewerAgent: Code review and best practices enforcement
- DocumentationAgent: Documentation generation and maintenance
"""

from .swarm_coordinator import SwarmCoordinator
from .architect_agent import ArchitectAgent
from .coder_agent import CoderAgent
from .tester_agent import TesterAgent
from .devops_agent import DevOpsAgent
from .security_agent import SecurityAgent
from .performance_agent import PerformanceAgent
from .reviewer_agent import ReviewerAgent
from .documentation_agent import DocumentationAgent

__all__ = [
    "SwarmCoordinator",
    "ArchitectAgent",
    "CoderAgent", 
    "TesterAgent",
    "DevOpsAgent",
    "SecurityAgent",
    "PerformanceAgent",
    "ReviewerAgent",
    "DocumentationAgent"
]
