"""
Coder Agent - Specialized in code generation and implementation.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class CoderAgent(BaseAgent):
    """Specialized agent for code generation and implementation."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("CoderAgent", [])
        self.gemini_client = gemini_client
    
    async def generate_code(self, specification: str, language: str = "python") -> TaskResult:
        """Generate code based on specification."""
        prompt = f"""
        As a senior software developer, implement the following specification:
        
        Specification: {specification}
        Language: {language}
        
        Provide:
        1. Clean, well-documented code
        2. Error handling
        3. Best practices implementation
        4. Performance optimizations
        5. Unit test suggestions
        """
        
        return await self.gemini_client.generate_response(prompt)
