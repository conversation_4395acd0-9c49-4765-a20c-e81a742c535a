"""
Google Gemini API client for the Augment Coding Assistant.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, AsyncGenerator
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
import aiohttp
import json

from ..core.config import AIConfig
from ..core.base import TaskResult


class GeminiClient:
    """
    Client for interacting with Google Gemini API.
    Supports both synchronous and asynchronous operations.
    """
    
    def __init__(self, config: AIConfig):
        self.config = config
        self.logger = logging.getLogger("gemini_client")
        
        # Configure Gemini
        if not config.api_key:
            raise ValueError("Gemini API key is required. Set GEMINI_API_KEY environment variable.")
        
        genai.configure(api_key=config.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel(
            model_name=config.model,
            generation_config=genai.types.GenerationConfig(
                temperature=config.temperature,
                max_output_tokens=config.max_tokens,
                top_p=0.95,
                top_k=64,
            ),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )
        
        self.logger.info(f"Initialized Gemini client with model: {config.model}")
    
    async def generate_response(self, prompt: str, context: Optional[List[Dict[str, str]]] = None) -> TaskResult:
        """
        Generate a response using Gemini.
        
        Args:
            prompt: The input prompt
            context: Optional conversation context
            
        Returns:
            TaskResult with the generated response
        """
        try:
            # Prepare the full prompt with context
            full_prompt = self._prepare_prompt(prompt, context)
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt
            )
            
            if response.text:
                return TaskResult(
                    success=True,
                    data=response.text,
                    metadata={
                        "model": self.config.model,
                        "prompt_tokens": len(full_prompt.split()),
                        "response_tokens": len(response.text.split())
                    }
                )
            else:
                return TaskResult(
                    success=False,
                    error="No response generated from Gemini"
                )
                
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return TaskResult(
                success=False,
                error=f"Gemini API error: {str(e)}"
            )
    
    async def generate_streaming_response(self, prompt: str, context: Optional[List[Dict[str, str]]] = None) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response using Gemini.
        
        Args:
            prompt: The input prompt
            context: Optional conversation context
            
        Yields:
            Chunks of the generated response
        """
        try:
            full_prompt = self._prepare_prompt(prompt, context)
            
            # Generate streaming response
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt,
                stream=True
            )
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            self.logger.error(f"Error in streaming response: {e}")
            yield f"Error: {str(e)}"
    
    def _prepare_prompt(self, prompt: str, context: Optional[List[Dict[str, str]]] = None) -> str:
        """Prepare the full prompt with context."""
        if not context:
            return prompt
        
        # Build conversation context
        context_str = ""
        for msg in context[-10:]:  # Last 10 messages for context
            role = msg.get("role", "user")
            content = msg.get("content", "")
            context_str += f"{role.title()}: {content}\n"
        
        return f"{context_str}\nUser: {prompt}\nAssistant:"
    
    async def analyze_code(self, code: str, language: str = "python", task: str = "analyze") -> TaskResult:
        """
        Analyze code using Gemini.
        
        Args:
            code: The code to analyze
            language: Programming language
            task: Type of analysis (analyze, review, optimize, debug)
            
        Returns:
            TaskResult with analysis
        """
        prompt = f"""
        Please {task} the following {language} code:

        ```{language}
        {code}
        ```

        Provide detailed insights including:
        - Code quality assessment
        - Potential issues or bugs
        - Optimization suggestions
        - Best practices recommendations
        - Security considerations (if applicable)
        """
        
        return await self.generate_response(prompt)
    
    async def generate_code(self, description: str, language: str = "python", style: str = "clean") -> TaskResult:
        """
        Generate code based on description.
        
        Args:
            description: Description of what the code should do
            language: Target programming language
            style: Code style (clean, optimized, documented)
            
        Returns:
            TaskResult with generated code
        """
        prompt = f"""
        Generate {style} {language} code for the following requirement:

        {description}

        Requirements:
        - Write clean, readable, and well-documented code
        - Follow best practices for {language}
        - Include error handling where appropriate
        - Add type hints (if applicable)
        - Include docstrings and comments
        
        Provide only the code with minimal explanation.
        """
        
        return await self.generate_response(prompt)
    
    async def explain_code(self, code: str, language: str = "python") -> TaskResult:
        """
        Explain what a piece of code does.
        
        Args:
            code: The code to explain
            language: Programming language
            
        Returns:
            TaskResult with explanation
        """
        prompt = f"""
        Please explain what this {language} code does:

        ```{language}
        {code}
        ```

        Provide:
        - High-level overview of functionality
        - Step-by-step breakdown
        - Key concepts and patterns used
        - Input/output description
        - Any notable features or techniques
        """
        
        return await self.generate_response(prompt)
    
    async def debug_code(self, code: str, error_message: str = "", language: str = "python") -> TaskResult:
        """
        Help debug code issues.
        
        Args:
            code: The problematic code
            error_message: Error message (if any)
            language: Programming language
            
        Returns:
            TaskResult with debugging suggestions
        """
        prompt = f"""
        Help debug this {language} code:

        ```{language}
        {code}
        ```

        {f"Error message: {error_message}" if error_message else ""}

        Please provide:
        - Identification of the issue(s)
        - Explanation of why the error occurs
        - Step-by-step solution
        - Corrected code
        - Prevention tips for similar issues
        """
        
        return await self.generate_response(prompt)
    
    async def optimize_code(self, code: str, language: str = "python", focus: str = "performance") -> TaskResult:
        """
        Optimize code for performance, readability, or other criteria.
        
        Args:
            code: The code to optimize
            language: Programming language
            focus: Optimization focus (performance, readability, memory, etc.)
            
        Returns:
            TaskResult with optimized code
        """
        prompt = f"""
        Optimize this {language} code for {focus}:

        ```{language}
        {code}
        ```

        Please provide:
        - Analysis of current code
        - Optimization opportunities
        - Optimized version of the code
        - Explanation of improvements made
        - Performance/quality metrics comparison
        """
        
        return await self.generate_response(prompt)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "model_name": self.config.model,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
            "provider": self.config.provider
        }
    
    async def shutdown(self) -> None:
        """Shutdown the client gracefully."""
        self.logger.info("Shutting down Gemini client")
        # No specific cleanup needed for Gemini client
