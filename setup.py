"""
Setup script for the Augment Coding Assistant.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
try:
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
except FileNotFoundError:
    pass

setup(
    name="augment-coding-assistant",
    version="1.0.0",
    author="Augment Code Agent",
    author_email="<EMAIL>",
    description="A comprehensive Python coding assistant with AI capabilities",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/augment-code/coding-assistant",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Software Development :: Code Generators",
        "Topic :: System :: Shells",
        "Topic :: Utilities",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "flake8>=6.1.0",
        ],
        "advanced": [
            "numpy>=1.24.0",
            "pandas>=2.1.0",
            "matplotlib>=3.8.0",
            "pillow>=10.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "augment-assistant=augment_assistant.main:main",
            "aca=augment_assistant.main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords=["ai", "coding", "assistant", "terminal", "automation", "gemini"],
    project_urls={
        "Bug Reports": "https://github.com/augment-code/coding-assistant/issues",
        "Source": "https://github.com/augment-code/coding-assistant",
        "Documentation": "https://github.com/augment-code/coding-assistant/docs",
    },
)
