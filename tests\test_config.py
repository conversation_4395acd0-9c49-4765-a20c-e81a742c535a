"""
Tests for configuration management.
"""

import pytest
import tempfile
import json
import yaml
from pathlib import Path

from augment_assistant.core.config import Config, AIConfig, TerminalConfig, FileSystemConfig, WebConfig


class TestConfig:
    """Test configuration management."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = Config()
        
        assert config.debug is False
        assert config.log_level == "INFO"
        assert config.ai.provider == "gemini"
        assert config.ai.model == "gemini-2.0-flash-exp"
        assert config.terminal.theme == "dark"
        assert config.filesystem.backup_enabled is True
        assert config.web.user_agent == "AugmentCodingAssistant/1.0"
    
    def test_ai_config(self):
        """Test AI configuration."""
        ai_config = AIConfig(
            provider="gemini",
            model="gemini-2.0-flash-exp",
            api_key="test-key",
            temperature=0.8,
            max_tokens=4096
        )
        
        assert ai_config.provider == "gemini"
        assert ai_config.model == "gemini-2.0-flash-exp"
        assert ai_config.api_key == "test-key"
        assert ai_config.temperature == 0.8
        assert ai_config.max_tokens == 4096
    
    def test_terminal_config(self):
        """Test terminal configuration."""
        terminal_config = TerminalConfig(
            theme="light",
            show_progress=False,
            auto_scroll=False,
            max_history=500
        )
        
        assert terminal_config.theme == "light"
        assert terminal_config.show_progress is False
        assert terminal_config.auto_scroll is False
        assert terminal_config.max_history == 500
    
    def test_filesystem_config(self):
        """Test filesystem configuration."""
        fs_config = FileSystemConfig(
            watch_directories=["/tmp", "/home"],
            ignore_patterns=["*.log", "*.tmp"],
            backup_enabled=False,
            max_file_size=5000000
        )
        
        assert fs_config.watch_directories == ["/tmp", "/home"]
        assert fs_config.ignore_patterns == ["*.log", "*.tmp"]
        assert fs_config.backup_enabled is False
        assert fs_config.max_file_size == 5000000
    
    def test_web_config(self):
        """Test web configuration."""
        web_config = WebConfig(
            user_agent="TestAgent/1.0",
            timeout=60,
            max_retries=5,
            delay_between_requests=2.0
        )
        
        assert web_config.user_agent == "TestAgent/1.0"
        assert web_config.timeout == 60
        assert web_config.max_retries == 5
        assert web_config.delay_between_requests == 2.0
    
    def test_config_save_load_json(self):
        """Test saving and loading configuration as JSON."""
        config = Config()
        config.debug = True
        config.log_level = "DEBUG"
        config.ai.temperature = 0.9
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_path = Path(f.name)
        
        try:
            config.save_to_file(config_path)
            loaded_config = Config.load_from_file(config_path)
            
            assert loaded_config.debug is True
            assert loaded_config.log_level == "DEBUG"
            assert loaded_config.ai.temperature == 0.9
        finally:
            config_path.unlink()
    
    def test_config_save_load_yaml(self):
        """Test saving and loading configuration as YAML."""
        config = Config()
        config.debug = True
        config.ai.model = "test-model"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_path = Path(f.name)
        
        try:
            config.save_to_file(config_path)
            loaded_config = Config.load_from_file(config_path)
            
            assert loaded_config.debug is True
            assert loaded_config.ai.model == "test-model"
        finally:
            config_path.unlink()
    
    def test_config_directories(self):
        """Test configuration directory creation."""
        config = Config()
        
        config_dir = config.get_config_dir()
        cache_dir = config.get_cache_dir()
        logs_dir = config.get_logs_dir()
        
        assert config_dir.exists()
        assert cache_dir.exists()
        assert logs_dir.exists()
        
        assert config_dir.name == ".augment_assistant"
        assert cache_dir.name == "cache"
        assert logs_dir.name == "logs"
    
    def test_invalid_config_file(self):
        """Test handling of invalid configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid content")
            config_path = Path(f.name)
        
        try:
            with pytest.raises(ValueError):
                Config.load_from_file(config_path)
        finally:
            config_path.unlink()
    
    def test_nonexistent_config_file(self):
        """Test loading non-existent configuration file."""
        config_path = Path("/nonexistent/config.yaml")
        config = Config.load_from_file(config_path)
        
        # Should return default config
        assert config.debug is False
        assert config.log_level == "INFO"


if __name__ == "__main__":
    pytest.main([__file__])
