"""
Reasoning engine implementing ReAct framework for the Augment Coding Assistant.
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any
import logging

from ..core.base import Reasoning<PERSON>hain, ReasoningStep, Memory, TaskResult
from ..core.config import Config
from .gemini_client import GeminiClient


class ReasoningEngine:
    """
    Implements the ReAct (Reasoning and Acting) framework for intelligent
    task planning and execution.
    """
    
    def __init__(self, gemini_client: GeminiClient, config: Config):
        self.gemini_client = gemini_client
        self.config = config
        self.logger = logging.getLogger("reasoning_engine")
        
        # ReAct prompts
        self.react_system_prompt = """
You are an intelligent coding assistant that uses the Re-Act (Reasoning and Acting) framework.

For each user request, you should:
1. THINK about what needs to be done
2. ACT by choosing appropriate actions
3. OBSERVE the results
4. Continue until the task is complete

Available capabilities and their actions:
- filesystem: read_file, write_file, list_directory, create_directory, delete_file, watch_directory
- terminal: execute_command, run_script, start_process, kill_process, get_process_status
- web: fetch_url, scrape_page, download_file, search_content
- code_analysis: parse_ast, analyze_code, find_functions, check_syntax, get_dependencies

Format your response as:
THINK: [Your reasoning about what to do next]
ACT: [capability:action:parameters]
OBSERVE: [What you expect to see or learn]

Continue this pattern until you can provide a final answer.
When done, end with:
FINAL: [Your final answer or result]
"""
    
    async def reason_and_act(
        self, 
        goal: str, 
        available_capabilities: List[str],
        context: Dict[str, Any] = None,
        memory: Memory = None,
        max_steps: int = 10
    ) -> ReasoningChain:
        """
        Use ReAct framework to reason about and execute a goal.
        
        Args:
            goal: The goal to achieve
            available_capabilities: List of available capability names
            context: Current context information
            memory: Memory system for retrieving relevant information
            max_steps: Maximum number of reasoning steps
            
        Returns:
            ReasoningChain with the complete reasoning process
        """
        reasoning_chain = ReasoningChain(goal=goal)
        
        try:
            # Prepare context for reasoning
            context_str = self._prepare_context(context, memory, available_capabilities)
            
            # Initial prompt
            prompt = f"""
{self.react_system_prompt}

GOAL: {goal}

CONTEXT:
{context_str}

Begin reasoning and acting to achieve this goal:
"""
            
            current_prompt = prompt
            step_count = 0
            
            while step_count < max_steps:
                step_count += 1
                
                # Generate reasoning step
                response = await self.gemini_client.generate_response(current_prompt)
                
                if not response.success:
                    reasoning_chain.add_step(
                        thought=f"Error in reasoning: {response.error}",
                        confidence=0.0
                    )
                    break
                
                # Parse the response
                step_info = self._parse_react_response(response.data)
                
                if not step_info:
                    reasoning_chain.add_step(
                        thought="Failed to parse reasoning response",
                        confidence=0.0
                    )
                    break
                
                # Add step to chain
                reasoning_chain.add_step(
                    thought=step_info.get("think", ""),
                    action=step_info.get("act", ""),
                    observation=step_info.get("observe", ""),
                    confidence=step_info.get("confidence", 0.8)
                )
                
                # Check if we have a final answer
                if step_info.get("final"):
                    reasoning_chain.complete(step_info["final"], success=True)
                    break
                
                # Prepare next prompt with conversation history
                current_prompt = self._build_continuation_prompt(reasoning_chain, step_info)
            
            # If we didn't get a final answer, mark as incomplete
            if not reasoning_chain.final_answer:
                reasoning_chain.complete("Task incomplete - reached maximum steps", success=False)
            
            self.logger.info(f"Reasoning completed in {step_count} steps")
            return reasoning_chain
            
        except Exception as e:
            self.logger.error(f"Error in reasoning: {e}")
            reasoning_chain.add_step(
                thought=f"Critical error in reasoning: {e}",
                confidence=0.0
            )
            reasoning_chain.complete(f"Error: {e}", success=False)
            return reasoning_chain
    
    def _prepare_context(
        self, 
        context: Dict[str, Any] = None, 
        memory: Memory = None,
        available_capabilities: List[str] = None
    ) -> str:
        """Prepare context string for reasoning."""
        context_parts = []
        
        # Add available capabilities
        if available_capabilities:
            context_parts.append(f"Available capabilities: {', '.join(available_capabilities)}")
        
        # Add current context
        if context:
            context_parts.append("Current context:")
            for key, value in context.items():
                context_parts.append(f"  {key}: {value}")
        
        # Add relevant memory
        if memory:
            recent_conversation = memory.get_recent_conversation(5)
            if recent_conversation:
                context_parts.append("Recent conversation:")
                for entry in recent_conversation:
                    role = entry.get("role", "unknown")
                    content = entry.get("content", "")[:200]  # Truncate long content
                    context_parts.append(f"  {role}: {content}")
        
        return "\n".join(context_parts) if context_parts else "No additional context available."
    
    def _parse_react_response(self, response: str) -> Optional[Dict[str, str]]:
        """Parse a ReAct formatted response."""
        try:
            result = {}
            
            # Extract THINK
            think_match = re.search(r'THINK:\s*(.*?)(?=ACT:|OBSERVE:|FINAL:|$)', response, re.DOTALL | re.IGNORECASE)
            if think_match:
                result["think"] = think_match.group(1).strip()
            
            # Extract ACT
            act_match = re.search(r'ACT:\s*(.*?)(?=OBSERVE:|THINK:|FINAL:|$)', response, re.DOTALL | re.IGNORECASE)
            if act_match:
                result["act"] = act_match.group(1).strip()
            
            # Extract OBSERVE
            observe_match = re.search(r'OBSERVE:\s*(.*?)(?=THINK:|ACT:|FINAL:|$)', response, re.DOTALL | re.IGNORECASE)
            if observe_match:
                result["observe"] = observe_match.group(1).strip()
            
            # Extract FINAL
            final_match = re.search(r'FINAL:\s*(.*?)$', response, re.DOTALL | re.IGNORECASE)
            if final_match:
                result["final"] = final_match.group(1).strip()
            
            # Estimate confidence based on response quality
            confidence = 0.8
            if result.get("think") and result.get("act"):
                confidence = 0.9
            elif not result.get("think") and not result.get("act"):
                confidence = 0.3
            
            result["confidence"] = confidence
            
            return result if result else None
            
        except Exception as e:
            self.logger.error(f"Error parsing ReAct response: {e}")
            return None
    
    def _build_continuation_prompt(self, reasoning_chain: ReasoningChain, last_step: Dict[str, str]) -> str:
        """Build prompt for continuing the reasoning process."""
        conversation = []
        
        # Add previous steps
        for step in reasoning_chain.steps:
            if step.thought:
                conversation.append(f"THINK: {step.thought}")
            if step.action:
                conversation.append(f"ACT: {step.action}")
            if step.observation:
                conversation.append(f"OBSERVE: {step.observation}")
        
        # Add instruction to continue
        conversation.append("\nContinue reasoning to achieve the goal. What should you do next?")
        
        return "\n".join(conversation)
    
    async def plan_task(self, task: str, context: Dict[str, Any] = None) -> TaskResult:
        """
        Create a high-level plan for a complex task.
        
        Args:
            task: The task to plan
            context: Additional context
            
        Returns:
            TaskResult with the plan
        """
        prompt = f"""
Create a detailed plan to accomplish this task: {task}

{f"Context: {context}" if context else ""}

Provide a step-by-step plan with:
1. Clear, actionable steps
2. Required capabilities for each step
3. Dependencies between steps
4. Expected outcomes
5. Potential challenges and mitigation strategies

Format as a structured plan with numbered steps.
"""
        
        return await self.gemini_client.generate_response(prompt)
    
    async def analyze_problem(self, problem: str, context: Dict[str, Any] = None) -> TaskResult:
        """
        Analyze a problem and suggest approaches.
        
        Args:
            problem: The problem to analyze
            context: Additional context
            
        Returns:
            TaskResult with analysis
        """
        prompt = f"""
Analyze this problem: {problem}

{f"Context: {context}" if context else ""}

Provide:
1. Problem breakdown and understanding
2. Root cause analysis
3. Multiple solution approaches
4. Pros and cons of each approach
5. Recommended solution with reasoning
6. Implementation considerations
"""
        
        return await self.gemini_client.generate_response(prompt)
    
    async def make_decision(
        self, 
        decision_point: str, 
        options: List[str], 
        criteria: List[str] = None,
        context: Dict[str, Any] = None
    ) -> TaskResult:
        """
        Make an informed decision between options.
        
        Args:
            decision_point: What decision needs to be made
            options: Available options
            criteria: Decision criteria
            context: Additional context
            
        Returns:
            TaskResult with decision and reasoning
        """
        criteria_str = f"Criteria: {', '.join(criteria)}" if criteria else ""
        options_str = "\n".join(f"{i+1}. {option}" for i, option in enumerate(options))
        
        prompt = f"""
Make a decision for: {decision_point}

Options:
{options_str}

{criteria_str}
{f"Context: {context}" if context else ""}

Provide:
1. Analysis of each option
2. Evaluation against criteria
3. Recommended choice with detailed reasoning
4. Potential risks and mitigation strategies
5. Implementation next steps
"""
        
        return await self.gemini_client.generate_response(prompt)
