"""
Code analysis and understanding capability for the Augment Coding Assistant.
"""

import ast
import inspect
import importlib
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Set
import re
import tokenize
import io
from collections import defaultdict
import json

try:
    import rope.base.project
    import rope.base.libutils
    import rope.contrib.codeassist
    import rope.contrib.autoimport
    ROPE_AVAILABLE = True
except ImportError:
    ROPE_AVAILABLE = False

try:
    import jedi
    JEDI_AVAILABLE = True
except ImportError:
    JEDI_AVAILABLE = False

from ..core.base import BaseCapability, TaskResult
from ..core.config import Config


class ASTAnalyzer:
    """AST-based code analysis."""
    
    def __init__(self):
        self.node_visitors = {
            ast.FunctionDef: self._analyze_function,
            ast.AsyncFunctionDef: self._analyze_function,
            ast.ClassDef: self._analyze_class,
            ast.Import: self._analyze_import,
            ast.ImportFrom: self._analyze_import_from,
            ast.Assign: self._analyze_assignment,
            ast.AnnAssign: self._analyze_annotated_assignment,
        }
    
    def analyze_code(self, code: str, filename: str = "<string>") -> Dict[str, Any]:
        """Analyze Python code using AST."""
        try:
            tree = ast.parse(code, filename=filename)
            
            analysis = {
                "filename": filename,
                "functions": [],
                "classes": [],
                "imports": [],
                "variables": [],
                "complexity": self._calculate_complexity(tree),
                "lines_of_code": len(code.splitlines()),
                "docstring": ast.get_docstring(tree),
                "syntax_errors": [],
                "warnings": [],
                "metrics": {}
            }
            
            # Walk the AST
            for node in ast.walk(tree):
                node_type = type(node)
                if node_type in self.node_visitors:
                    result = self.node_visitors[node_type](node)
                    if result:
                        category = self._get_category_for_node(node_type)
                        if category in analysis:
                            analysis[category].append(result)
            
            # Calculate additional metrics
            analysis["metrics"] = self._calculate_metrics(analysis, tree)
            
            return analysis
            
        except SyntaxError as e:
            return {
                "filename": filename,
                "syntax_errors": [{
                    "line": e.lineno,
                    "column": e.offset,
                    "message": str(e),
                    "text": e.text
                }],
                "valid": False
            }
        except Exception as e:
            return {
                "filename": filename,
                "error": str(e),
                "valid": False
            }
    
    def _analyze_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> Dict[str, Any]:
        """Analyze function definition."""
        return {
            "name": node.name,
            "type": "async_function" if isinstance(node, ast.AsyncFunctionDef) else "function",
            "line": node.lineno,
            "args": [arg.arg for arg in node.args.args],
            "defaults": len(node.args.defaults),
            "vararg": node.args.vararg.arg if node.args.vararg else None,
            "kwarg": node.args.kwarg.arg if node.args.kwarg else None,
            "decorators": [self._get_decorator_name(d) for d in node.decorator_list],
            "docstring": ast.get_docstring(node),
            "complexity": self._calculate_function_complexity(node),
            "returns": self._get_return_annotation(node),
            "is_method": False,  # Will be updated if inside a class
            "is_private": node.name.startswith('_'),
            "is_dunder": node.name.startswith('__') and node.name.endswith('__')
        }
    
    def _analyze_class(self, node: ast.ClassDef) -> Dict[str, Any]:
        """Analyze class definition."""
        methods = []
        attributes = []
        
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_info = self._analyze_function(item)
                method_info["is_method"] = True
                method_info["is_classmethod"] = any(
                    self._get_decorator_name(d) == "classmethod" for d in item.decorator_list
                )
                method_info["is_staticmethod"] = any(
                    self._get_decorator_name(d) == "staticmethod" for d in item.decorator_list
                )
                method_info["is_property"] = any(
                    self._get_decorator_name(d) == "property" for d in item.decorator_list
                )
                methods.append(method_info)
            elif isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        attributes.append({
                            "name": target.id,
                            "line": item.lineno,
                            "type": "class_attribute"
                        })
        
        return {
            "name": node.name,
            "line": node.lineno,
            "bases": [self._get_base_name(base) for base in node.bases],
            "decorators": [self._get_decorator_name(d) for d in node.decorator_list],
            "docstring": ast.get_docstring(node),
            "methods": methods,
            "attributes": attributes,
            "is_abstract": any("ABC" in self._get_base_name(base) for base in node.bases),
            "method_count": len(methods),
            "attribute_count": len(attributes)
        }
    
    def _analyze_import(self, node: ast.Import) -> Dict[str, Any]:
        """Analyze import statement."""
        return {
            "type": "import",
            "line": node.lineno,
            "modules": [{"name": alias.name, "alias": alias.asname} for alias in node.names]
        }
    
    def _analyze_import_from(self, node: ast.ImportFrom) -> Dict[str, Any]:
        """Analyze from import statement."""
        return {
            "type": "from_import",
            "line": node.lineno,
            "module": node.module,
            "level": node.level,
            "names": [{"name": alias.name, "alias": alias.asname} for alias in node.names]
        }
    
    def _analyze_assignment(self, node: ast.Assign) -> Dict[str, Any]:
        """Analyze assignment statement."""
        targets = []
        for target in node.targets:
            if isinstance(target, ast.Name):
                targets.append(target.id)
        
        return {
            "line": node.lineno,
            "targets": targets,
            "type": "assignment"
        }
    
    def _analyze_annotated_assignment(self, node: ast.AnnAssign) -> Dict[str, Any]:
        """Analyze annotated assignment."""
        target_name = None
        if isinstance(node.target, ast.Name):
            target_name = node.target.id
        
        return {
            "line": node.lineno,
            "target": target_name,
            "annotation": ast.unparse(node.annotation) if hasattr(ast, 'unparse') else str(node.annotation),
            "type": "annotated_assignment"
        }
    
    def _get_decorator_name(self, decorator) -> str:
        """Get decorator name."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{decorator.value.id}.{decorator.attr}" if isinstance(decorator.value, ast.Name) else decorator.attr
        elif isinstance(decorator, ast.Call):
            return self._get_decorator_name(decorator.func)
        return str(decorator)
    
    def _get_base_name(self, base) -> str:
        """Get base class name."""
        if isinstance(base, ast.Name):
            return base.id
        elif isinstance(base, ast.Attribute):
            return f"{base.value.id}.{base.attr}" if isinstance(base.value, ast.Name) else base.attr
        return str(base)
    
    def _get_return_annotation(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> Optional[str]:
        """Get return type annotation."""
        if node.returns:
            if hasattr(ast, 'unparse'):
                return ast.unparse(node.returns)
            else:
                return str(node.returns)
        return None
    
    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, ast.comprehension):
                complexity += 1
        
        return complexity
    
    def _calculate_function_complexity(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> int:
        """Calculate complexity for a specific function."""
        complexity = 1
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _calculate_metrics(self, analysis: Dict[str, Any], tree: ast.AST) -> Dict[str, Any]:
        """Calculate code metrics."""
        return {
            "total_functions": len(analysis.get("functions", [])),
            "total_classes": len(analysis.get("classes", [])),
            "total_imports": len(analysis.get("imports", [])),
            "avg_function_complexity": self._avg_complexity(analysis.get("functions", [])),
            "max_function_complexity": self._max_complexity(analysis.get("functions", [])),
            "total_lines": analysis.get("lines_of_code", 0),
            "has_docstring": analysis.get("docstring") is not None,
            "docstring_coverage": self._calculate_docstring_coverage(analysis)
        }
    
    def _avg_complexity(self, functions: List[Dict[str, Any]]) -> float:
        """Calculate average function complexity."""
        if not functions:
            return 0.0
        complexities = [f.get("complexity", 1) for f in functions]
        return sum(complexities) / len(complexities)
    
    def _max_complexity(self, functions: List[Dict[str, Any]]) -> int:
        """Calculate maximum function complexity."""
        if not functions:
            return 0
        return max(f.get("complexity", 1) for f in functions)
    
    def _calculate_docstring_coverage(self, analysis: Dict[str, Any]) -> float:
        """Calculate docstring coverage percentage."""
        total_items = 0
        documented_items = 0
        
        # Count module docstring
        total_items += 1
        if analysis.get("docstring"):
            documented_items += 1
        
        # Count function docstrings
        functions = analysis.get("functions", [])
        total_items += len(functions)
        documented_items += sum(1 for f in functions if f.get("docstring"))
        
        # Count class docstrings
        classes = analysis.get("classes", [])
        total_items += len(classes)
        documented_items += sum(1 for c in classes if c.get("docstring"))
        
        # Count method docstrings
        for cls in classes:
            methods = cls.get("methods", [])
            total_items += len(methods)
            documented_items += sum(1 for m in methods if m.get("docstring"))
        
        return (documented_items / total_items * 100) if total_items > 0 else 0.0
    
    def _get_category_for_node(self, node_type) -> str:
        """Get category name for AST node type."""
        if node_type in (ast.FunctionDef, ast.AsyncFunctionDef):
            return "functions"
        elif node_type == ast.ClassDef:
            return "classes"
        elif node_type in (ast.Import, ast.ImportFrom):
            return "imports"
        elif node_type in (ast.Assign, ast.AnnAssign):
            return "variables"
        return "other"


class CodeAnalysisAgent(BaseCapability):
    """
    Comprehensive code analysis with AST parsing, static analysis,
    and intelligent code understanding.
    """
    
    def __init__(self, config: Config):
        super().__init__("code_analysis", "Code analysis and understanding")
        self.config = config
        self.ast_analyzer = ASTAnalyzer()
        self.rope_project = None
        
        # Initialize Rope project if available
        if ROPE_AVAILABLE:
            try:
                self.rope_project = rope.base.project.Project(str(config.working_directory))
            except Exception as e:
                self.log_error(f"Failed to initialize Rope project: {e}")
    
    async def execute(self, task: str, **kwargs) -> TaskResult:
        """Execute a code analysis task."""
        parameters = kwargs.get("parameters", "")
        
        try:
            if task == "parse_ast":
                return await self._parse_ast(parameters)
            elif task == "analyze_code":
                return await self._analyze_code(parameters)
            elif task == "find_functions":
                return await self._find_functions(parameters)
            elif task == "find_classes":
                return await self._find_classes(parameters)
            elif task == "check_syntax":
                return await self._check_syntax(parameters)
            elif task == "get_dependencies":
                return await self._get_dependencies(parameters)
            elif task == "analyze_complexity":
                return await self._analyze_complexity(parameters)
            elif task == "find_issues":
                return await self._find_issues(parameters)
            elif task == "get_suggestions":
                return await self._get_suggestions(parameters)
            elif task == "analyze_imports":
                return await self._analyze_imports(parameters)
            elif task == "find_unused_code":
                return await self._find_unused_code(parameters)
            elif task == "get_call_graph":
                return await self._get_call_graph(parameters)
            else:
                return TaskResult(success=False, error=f"Unknown task: {task}")
                
        except Exception as e:
            self.log_error(f"Error executing {task}: {e}")
            return TaskResult(success=False, error=str(e))
    
    def get_available_actions(self) -> List[str]:
        """Get list of available code analysis actions."""
        return [
            "parse_ast", "analyze_code", "find_functions", "find_classes",
            "check_syntax", "get_dependencies", "analyze_complexity",
            "find_issues", "get_suggestions", "analyze_imports",
            "find_unused_code", "get_call_graph"
        ]

    async def _parse_ast(self, file_path: str) -> TaskResult:
        """Parse AST for a Python file."""
        try:
            path = Path(file_path.strip())

            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")

            if path.suffix != '.py':
                return TaskResult(success=False, error=f"Not a Python file: {path}")

            with open(path, 'r', encoding='utf-8') as f:
                code = f.read()

            analysis = self.ast_analyzer.analyze_code(code, str(path))

            return TaskResult(
                success=True,
                data=analysis,
                metadata={"file_path": str(path), "analysis_type": "ast"}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error parsing AST: {e}")

    async def _analyze_code(self, params: str) -> TaskResult:
        """Comprehensive code analysis. Format: file_path|analysis_type"""
        try:
            parts = params.split("|", 1)
            file_path = parts[0].strip()
            analysis_type = parts[1].strip() if len(parts) > 1 else "full"

            path = Path(file_path)

            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")

            with open(path, 'r', encoding='utf-8') as f:
                code = f.read()

            # AST analysis
            ast_analysis = self.ast_analyzer.analyze_code(code, str(path))

            # Additional analysis based on type
            analysis_result = {
                "file_path": str(path),
                "ast_analysis": ast_analysis,
                "code_quality": self._assess_code_quality(ast_analysis),
                "recommendations": self._generate_recommendations(ast_analysis)
            }

            # Add Jedi analysis if available
            if JEDI_AVAILABLE and analysis_type in ("full", "jedi"):
                try:
                    jedi_analysis = self._jedi_analysis(code, str(path))
                    analysis_result["jedi_analysis"] = jedi_analysis
                except Exception as e:
                    self.log_error(f"Jedi analysis failed: {e}")

            return TaskResult(
                success=True,
                data=analysis_result,
                metadata={"file_path": str(path), "analysis_type": analysis_type}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error analyzing code: {e}")

    def _assess_code_quality(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess code quality based on analysis."""
        quality = {
            "score": 0,
            "issues": [],
            "strengths": [],
            "overall_rating": "poor"
        }

        score = 0
        max_score = 100

        # Check docstring coverage
        docstring_coverage = analysis.get("metrics", {}).get("docstring_coverage", 0)
        if docstring_coverage > 80:
            score += 20
            quality["strengths"].append("Excellent docstring coverage")
        elif docstring_coverage > 50:
            score += 10
            quality["strengths"].append("Good docstring coverage")
        else:
            quality["issues"].append("Poor docstring coverage")

        # Check complexity
        avg_complexity = analysis.get("metrics", {}).get("avg_function_complexity", 0)
        max_complexity = analysis.get("metrics", {}).get("max_function_complexity", 0)

        if max_complexity > 10:
            quality["issues"].append(f"High complexity function detected (complexity: {max_complexity})")
        elif avg_complexity < 3:
            score += 15
            quality["strengths"].append("Low average complexity")

        # Check for syntax errors
        if not analysis.get("syntax_errors"):
            score += 20
            quality["strengths"].append("No syntax errors")
        else:
            quality["issues"].append("Syntax errors present")

        # Check function count vs file size
        lines = analysis.get("lines_of_code", 0)
        functions = len(analysis.get("functions", []))

        if lines > 0:
            if functions / lines > 0.1:  # Too many small functions
                quality["issues"].append("Possibly over-fragmented code")
            elif functions / lines < 0.01 and lines > 100:  # Too few functions for large file
                quality["issues"].append("Large file with few functions - consider refactoring")
            else:
                score += 10

        # Check for proper naming conventions
        functions = analysis.get("functions", [])
        classes = analysis.get("classes", [])

        naming_issues = 0
        for func in functions:
            if not func["name"].islower() and not func["is_dunder"]:
                naming_issues += 1

        for cls in classes:
            if not cls["name"][0].isupper():
                naming_issues += 1

        if naming_issues == 0:
            score += 15
            quality["strengths"].append("Good naming conventions")
        else:
            quality["issues"].append(f"Naming convention issues: {naming_issues}")

        # Final scoring
        quality["score"] = min(score, max_score)

        if quality["score"] >= 80:
            quality["overall_rating"] = "excellent"
        elif quality["score"] >= 60:
            quality["overall_rating"] = "good"
        elif quality["score"] >= 40:
            quality["overall_rating"] = "fair"
        else:
            quality["overall_rating"] = "poor"

        return quality

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate code improvement recommendations."""
        recommendations = []

        # Docstring recommendations
        docstring_coverage = analysis.get("metrics", {}).get("docstring_coverage", 0)
        if docstring_coverage < 50:
            recommendations.append("Add docstrings to functions and classes for better documentation")

        # Complexity recommendations
        max_complexity = analysis.get("metrics", {}).get("max_function_complexity", 0)
        if max_complexity > 10:
            recommendations.append("Consider breaking down complex functions (complexity > 10)")

        # Import recommendations
        imports = analysis.get("imports", [])
        if len(imports) > 20:
            recommendations.append("Consider organizing imports or reducing dependencies")

        # Function length recommendations
        functions = analysis.get("functions", [])
        for func in functions:
            if func.get("complexity", 1) > 15:
                recommendations.append(f"Function '{func['name']}' is very complex - consider refactoring")

        # Class recommendations
        classes = analysis.get("classes", [])
        for cls in classes:
            if cls.get("method_count", 0) > 20:
                recommendations.append(f"Class '{cls['name']}' has many methods - consider splitting")

        return recommendations

    def _jedi_analysis(self, code: str, file_path: str) -> Dict[str, Any]:
        """Perform Jedi-based analysis."""
        try:
            script = jedi.Script(code=code, path=file_path)

            # Get completions at various points
            completions = []
            definitions = []

            # Get names (variables, functions, classes)
            names = script.get_names(all_scopes=True)

            jedi_info = {
                "names": [
                    {
                        "name": name.name,
                        "type": name.type,
                        "line": name.line,
                        "column": name.column,
                        "description": name.description,
                        "full_name": name.full_name
                    }
                    for name in names
                ],
                "total_names": len(names),
                "name_types": {}
            }

            # Count name types
            for name in names:
                name_type = name.type
                jedi_info["name_types"][name_type] = jedi_info["name_types"].get(name_type, 0) + 1

            return jedi_info

        except Exception as e:
            return {"error": str(e)}

    async def _find_functions(self, file_path: str) -> TaskResult:
        """Find all functions in a file."""
        try:
            analysis_result = await self._parse_ast(file_path)
            if not analysis_result.success:
                return analysis_result

            functions = analysis_result.data.get("functions", [])

            return TaskResult(
                success=True,
                data=functions,
                metadata={"file_path": file_path, "function_count": len(functions)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error finding functions: {e}")

    async def _find_classes(self, file_path: str) -> TaskResult:
        """Find all classes in a file."""
        try:
            analysis_result = await self._parse_ast(file_path)
            if not analysis_result.success:
                return analysis_result

            classes = analysis_result.data.get("classes", [])

            return TaskResult(
                success=True,
                data=classes,
                metadata={"file_path": file_path, "class_count": len(classes)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error finding classes: {e}")

    async def _check_syntax(self, file_path: str) -> TaskResult:
        """Check syntax of a Python file."""
        try:
            path = Path(file_path.strip())

            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")

            with open(path, 'r', encoding='utf-8') as f:
                code = f.read()

            try:
                ast.parse(code, filename=str(path))
                return TaskResult(
                    success=True,
                    data={"valid": True, "message": "Syntax is valid"},
                    metadata={"file_path": str(path)}
                )
            except SyntaxError as e:
                return TaskResult(
                    success=True,
                    data={
                        "valid": False,
                        "error": {
                            "line": e.lineno,
                            "column": e.offset,
                            "message": str(e),
                            "text": e.text
                        }
                    },
                    metadata={"file_path": str(path)}
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error checking syntax: {e}")

    async def _get_dependencies(self, file_path: str) -> TaskResult:
        """Get dependencies (imports) from a file."""
        try:
            analysis_result = await self._parse_ast(file_path)
            if not analysis_result.success:
                return analysis_result

            imports = analysis_result.data.get("imports", [])

            # Organize imports
            standard_lib = []
            third_party = []
            local = []

            for imp in imports:
                if imp["type"] == "import":
                    for module in imp["modules"]:
                        module_name = module["name"].split('.')[0]
                        if self._is_standard_library(module_name):
                            standard_lib.append(module)
                        elif '.' in module["name"] or module_name.startswith('.'):
                            local.append(module)
                        else:
                            third_party.append(module)
                elif imp["type"] == "from_import":
                    module_name = imp["module"]
                    if module_name:
                        base_module = module_name.split('.')[0]
                        if self._is_standard_library(base_module):
                            standard_lib.extend(imp["names"])
                        elif module_name.startswith('.'):
                            local.extend(imp["names"])
                        else:
                            third_party.extend(imp["names"])

            dependencies = {
                "standard_library": standard_lib,
                "third_party": third_party,
                "local": local,
                "total_imports": len(imports),
                "all_imports": imports
            }

            return TaskResult(
                success=True,
                data=dependencies,
                metadata={"file_path": file_path}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting dependencies: {e}")

    def _is_standard_library(self, module_name: str) -> bool:
        """Check if a module is part of the standard library."""
        try:
            # Try to import and check if it's a built-in module
            spec = importlib.util.find_spec(module_name)
            if spec is None:
                return False

            # Check if it's in the standard library path
            if spec.origin:
                return 'site-packages' not in spec.origin and 'dist-packages' not in spec.origin

            return module_name in sys.builtin_module_names
        except:
            return False

    async def shutdown(self) -> None:
        """Shutdown the code analysis agent."""
        if self.rope_project:
            try:
                self.rope_project.close()
                self.log_info("Rope project closed")
            except Exception as e:
                self.log_error(f"Error closing Rope project: {e}")

        self.log_info("Code analysis agent shutdown complete")
