# 📦 Installation Guide

This guide will help you install and set up the Augment Coding Assistant on your system.

## 🔧 Prerequisites

### System Requirements
- **Python**: 3.9 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: At least 4GB RAM recommended
- **Storage**: 500MB free space for installation

### Required API Keys
- **Google Gemini API Key**: Required for AI functionality
  - Get your API key from [Google AI Studio](https://aistudio.google.com/)
  - Free tier available with generous limits

## 🚀 Installation Methods

### Method 1: Quick Install (Recommended)

1. **Install from PyPI** (when published):
```bash
pip install augment-coding-assistant
```

2. **Set up API key**:
```bash
export GEMINI_API_KEY="your-api-key-here"
```

3. **Verify installation**:
```bash
augment-assistant version
```

### Method 2: Development Install

1. **Clone the repository**:
```bash
git clone https://github.com/augment-code/coding-assistant.git
cd coding-assistant
```

2. **Create virtual environment** (recommended):
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Install in development mode**:
```bash
pip install -e .
```

5. **Set up environment variables**:
```bash
# Create .env file
echo "GEMINI_API_KEY=your-api-key-here" > .env
```

### Method 3: Docker Install

1. **Build Docker image**:
```bash
docker build -t augment-assistant .
```

2. **Run container**:
```bash
docker run -it --rm \
  -e GEMINI_API_KEY=your-api-key \
  -v $(pwd):/workspace \
  augment-assistant
```

## 🔑 API Key Setup

### Getting Your Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Navigate to "Get API Key"
4. Create a new API key
5. Copy the key for use in the assistant

### Setting Up the API Key

#### Option 1: Environment Variable
```bash
# Linux/macOS
export GEMINI_API_KEY="your-api-key-here"

# Windows Command Prompt
set GEMINI_API_KEY=your-api-key-here

# Windows PowerShell
$env:GEMINI_API_KEY="your-api-key-here"
```

#### Option 2: Configuration File
```yaml
# config.yaml
ai:
  api_key: "your-api-key-here"
  model: "gemini-2.0-flash-exp"
```

#### Option 3: .env File
```bash
# .env file in project root
GEMINI_API_KEY=your-api-key-here
AI_MODEL=gemini-2.0-flash-exp
DEBUG=false
```

## 🔧 Optional Dependencies

### For Enhanced Web Scraping
```bash
# Install Playwright browsers
playwright install
```

### For Advanced Code Analysis
```bash
# Install additional analysis tools
pip install rope jedi pylint black isort
```

### For Development
```bash
# Install development dependencies
pip install -e ".[dev]"
```

## ✅ Verification

### Test Basic Functionality
```bash
# Check version
augment-assistant version

# Test configuration
augment-assistant config-show

# Test capabilities
augment-assistant capabilities list
```

### Run Test Suite
```bash
# Run all tests
pytest

# Run quick tests only
pytest -m "not slow"
```

### Test Interactive Mode
```bash
# Start interactive session
augment-assistant interactive

# Try a simple command
> help
> status
> Create a test file with some content
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Solution: Ensure all dependencies are installed
pip install -r requirements.txt
```

#### 2. API Key Not Found
```bash
# Check if API key is set
echo $GEMINI_API_KEY

# Set it if missing
export GEMINI_API_KEY="your-key"
```

#### 3. Permission Errors
```bash
# On Linux/macOS, you might need to set permissions
chmod +x augment-assistant
```

#### 4. Playwright Issues
```bash
# Install browsers if web scraping fails
playwright install chromium
```

### Platform-Specific Issues

#### Windows
- Use PowerShell or Command Prompt as Administrator if needed
- Ensure Python is in your PATH
- Use `python -m pip` instead of `pip` if command not found

#### macOS
- Install Xcode Command Line Tools: `xcode-select --install`
- Use Homebrew for Python if system Python causes issues

#### Linux
- Install system dependencies:
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3-dev python3-pip

# CentOS/RHEL
sudo yum install python3-devel python3-pip
```

## 🔄 Updating

### Update from PyPI
```bash
pip install --upgrade augment-coding-assistant
```

### Update Development Install
```bash
cd coding-assistant
git pull origin main
pip install -r requirements.txt
```

## 🗑️ Uninstallation

### Remove Package
```bash
pip uninstall augment-coding-assistant
```

### Clean Up Files
```bash
# Remove configuration directory
rm -rf ~/.augment_assistant

# Remove any created files (optional)
rm -rf ~/.cache/augment_assistant
```

## 📞 Support

If you encounter any issues during installation:

1. **Check the logs**: Look in `~/.augment_assistant/logs/`
2. **Search existing issues**: [GitHub Issues](https://github.com/augment-code/coding-assistant/issues)
3. **Create a new issue**: Include your OS, Python version, and error messages
4. **Join discussions**: [GitHub Discussions](https://github.com/augment-code/coding-assistant/discussions)

## 🎯 Next Steps

After successful installation:

1. **Read the [User Guide](USER_GUIDE.md)** for detailed usage instructions
2. **Check out [Examples](EXAMPLES.md)** for common use cases
3. **Configure the assistant** according to your preferences
4. **Start coding** with your new AI-powered assistant!

---

**Happy coding! 🚀**
