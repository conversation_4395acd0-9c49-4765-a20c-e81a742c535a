"""
Documentation Agent - Specialized in documentation generation.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class DocumentationAgent(BaseAgent):
    """Specialized agent for documentation generation."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("DocumentationAgent", [])
        self.gemini_client = gemini_client
    
    async def generate_documentation(self, code: str, doc_type: str = "api") -> TaskResult:
        """Generate comprehensive documentation."""
        prompt = f"""
        As a senior technical writer, create documentation for:
        
        Code: {code}
        Documentation Type: {doc_type}
        
        Provide:
        1. Clear API documentation
        2. Usage examples
        3. Installation instructions
        4. Configuration guide
        5. Troubleshooting section
        """
        
        return await self.gemini_client.generate_response(prompt)
