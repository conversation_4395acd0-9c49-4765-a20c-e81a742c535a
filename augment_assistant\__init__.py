"""
Augment Coding Assistant - A comprehensive Python coding assistant with AI capabilities.

This package provides a powerful coding assistant that combines:
- AI-powered reasoning and planning using Google Gemini
- File system operations and monitoring
- Terminal integration and process management
- Web scraping and data fetching
- Code analysis and generation
- Beautiful terminal interface

Author: Augment Code Agent
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Augment Code Agent"
__email__ = "<EMAIL>"

from .main import main
from .core.agent import AugmentCodingAssistant
from .core.config import Config

__all__ = [
    "main",
    "AugmentCodingAssistant", 
    "Config",
    "__version__",
    "__author__",
    "__email__"
]
