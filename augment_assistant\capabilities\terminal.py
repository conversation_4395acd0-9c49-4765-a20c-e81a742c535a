"""
Terminal and process management capability for the Augment Coding Assistant.
"""

import asyncio
import subprocess
import os
import signal
import sys
import psutil
import pexpect
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import shlex
import time
from datetime import datetime
import threading
import queue

from ..core.base import BaseCapability, TaskResult
from ..core.config import Config


class ProcessManager:
    """Manages running processes and their states."""
    
    def __init__(self):
        self.processes = {}
        self.interactive_sessions = {}
    
    def add_process(self, pid: int, command: str, process_type: str = "subprocess") -> None:
        """Add a process to tracking."""
        self.processes[pid] = {
            "pid": pid,
            "command": command,
            "type": process_type,
            "started": datetime.now().isoformat(),
            "status": "running"
        }
    
    def remove_process(self, pid: int) -> None:
        """Remove a process from tracking."""
        if pid in self.processes:
            del self.processes[pid]
    
    def get_process_info(self, pid: int) -> Optional[Dict[str, Any]]:
        """Get information about a tracked process."""
        return self.processes.get(pid)
    
    def list_processes(self) -> List[Dict[str, Any]]:
        """List all tracked processes."""
        return list(self.processes.values())
    
    def cleanup_finished_processes(self) -> None:
        """Remove finished processes from tracking."""
        finished_pids = []
        for pid in self.processes:
            try:
                process = psutil.Process(pid)
                if not process.is_running():
                    finished_pids.append(pid)
            except psutil.NoSuchProcess:
                finished_pids.append(pid)
        
        for pid in finished_pids:
            self.remove_process(pid)


class TerminalAgent(BaseCapability):
    """
    Terminal and process management with support for both simple commands
    and interactive sessions.
    """
    
    def __init__(self, config: Config):
        super().__init__("terminal", "Terminal execution and process management")
        self.config = config
        self.process_manager = ProcessManager()
        self.default_timeout = 30
        self.max_output_size = 1024 * 1024  # 1MB
    
    async def execute(self, task: str, **kwargs) -> TaskResult:
        """Execute a terminal task."""
        parameters = kwargs.get("parameters", "")
        
        try:
            if task == "execute_command":
                return await self._execute_command(parameters)
            elif task == "run_script":
                return await self._run_script(parameters)
            elif task == "start_process":
                return await self._start_process(parameters)
            elif task == "kill_process":
                return await self._kill_process(parameters)
            elif task == "get_process_status":
                return await self._get_process_status(parameters)
            elif task == "list_processes":
                return await self._list_processes()
            elif task == "start_interactive":
                return await self._start_interactive_session(parameters)
            elif task == "send_to_interactive":
                return await self._send_to_interactive(parameters)
            elif task == "stop_interactive":
                return await self._stop_interactive_session(parameters)
            elif task == "get_system_info":
                return await self._get_system_info()
            elif task == "change_directory":
                return await self._change_directory(parameters)
            elif task == "get_environment":
                return await self._get_environment(parameters)
            elif task == "set_environment":
                return await self._set_environment(parameters)
            # Enhanced operations
            elif task == "run_in_terminal":
                return await self._run_in_terminal(parameters)
            elif task == "get_terminal_output":
                return await self._get_terminal_output(parameters)
            elif task == "get_terminal_last_command":
                return await self._get_terminal_last_command(parameters)
            elif task == "get_terminal_selection":
                return await self._get_terminal_selection(parameters)
            elif task == "get_task_output":
                return await self._get_task_output(parameters)
            elif task == "create_and_run_task":
                return await self._create_and_run_task(parameters)
            elif task == "install_python_packages":
                return await self._install_python_packages(parameters)
            elif task == "configure_python_environment":
                return await self._configure_python_environment(parameters)
            # Testing and debugging
            elif task == "test_search":
                return await self._test_search(parameters)
            elif task == "test_failure":
                return await self._test_failure(parameters)
            elif task == "run_tests":
                return await self._run_tests(parameters)
            elif task == "autonomous_debugger":
                return await self._autonomous_debugger(parameters)
            elif task == "lint_check":
                return await self._lint_check(parameters)
            elif task == "self_repair":
                return await self._self_repair(parameters)
            elif task == "code_linting_static_analysis":
                return await self._code_linting_static_analysis(parameters)
            else:
                return TaskResult(success=False, error=f"Unknown task: {task}")
                
        except Exception as e:
            self.log_error(f"Error executing {task}: {e}")
            return TaskResult(success=False, error=str(e))
    
    def get_available_actions(self) -> List[str]:
        """Get list of available terminal actions."""
        return [
            # Basic terminal operations
            "execute_command", "run_script", "start_process", "kill_process",
            "get_process_status", "list_processes", "start_interactive",
            "send_to_interactive", "stop_interactive", "get_system_info",
            "change_directory", "get_environment", "set_environment",
            # Enhanced operations
            "run_in_terminal", "get_terminal_output", "get_terminal_last_command",
            "get_terminal_selection", "get_task_output", "create_and_run_task",
            "install_python_packages", "configure_python_environment",
            # Testing and debugging
            "test_search", "test_failure", "run_tests", "autonomous_debugger",
            "lint_check", "self_repair", "code_linting_static_analysis"
        ]
    
    async def _execute_command(self, command: str) -> TaskResult:
        """Execute a command and return the result."""
        try:
            command = command.strip()
            if not command:
                return TaskResult(success=False, error="Empty command")
            
            self.log_info(f"Executing command: {command}")
            
            # Use asyncio subprocess for better async support
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.config.working_directory
            )
            
            # Track the process
            self.process_manager.add_process(process.pid, command)
            
            try:
                # Wait for completion with timeout
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.default_timeout
                )
                
                # Decode output
                stdout_text = stdout.decode('utf-8', errors='replace')
                stderr_text = stderr.decode('utf-8', errors='replace')
                
                # Limit output size
                if len(stdout_text) > self.max_output_size:
                    stdout_text = stdout_text[:self.max_output_size] + "\n... (output truncated)"
                
                if len(stderr_text) > self.max_output_size:
                    stderr_text = stderr_text[:self.max_output_size] + "\n... (output truncated)"
                
                success = process.returncode == 0
                
                result_data = {
                    "command": command,
                    "return_code": process.returncode,
                    "stdout": stdout_text,
                    "stderr": stderr_text,
                    "success": success
                }
                
                return TaskResult(
                    success=success,
                    data=result_data,
                    metadata={
                        "pid": process.pid,
                        "return_code": process.returncode,
                        "execution_time": "completed"
                    }
                )
                
            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
                
                return TaskResult(
                    success=False,
                    error=f"Command timed out after {self.default_timeout} seconds",
                    metadata={"pid": process.pid, "timeout": True}
                )
            
            finally:
                self.process_manager.remove_process(process.pid)
                
        except Exception as e:
            return TaskResult(success=False, error=f"Error executing command: {e}")
    
    async def _run_script(self, params: str) -> TaskResult:
        """Run a script file. Format: script_path|interpreter"""
        try:
            parts = params.split("|", 1)
            script_path = parts[0].strip()
            interpreter = parts[1].strip() if len(parts) > 1 else None
            
            path = Path(script_path)
            if not path.exists():
                return TaskResult(success=False, error=f"Script not found: {script_path}")
            
            # Determine interpreter if not provided
            if not interpreter:
                if path.suffix == '.py':
                    interpreter = 'python'
                elif path.suffix == '.sh':
                    interpreter = 'bash'
                elif path.suffix == '.js':
                    interpreter = 'node'
                elif path.suffix == '.ps1':
                    interpreter = 'powershell'
                else:
                    # Try to execute directly
                    command = str(path)
            
            if interpreter:
                command = f"{interpreter} {shlex.quote(str(path))}"
            
            return await self._execute_command(command)
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error running script: {e}")
    
    async def _start_process(self, command: str) -> TaskResult:
        """Start a background process."""
        try:
            command = command.strip()
            if not command:
                return TaskResult(success=False, error="Empty command")
            
            self.log_info(f"Starting background process: {command}")
            
            # Start process in background
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.config.working_directory
            )
            
            # Track the process
            self.process_manager.add_process(process.pid, command, "background")
            
            return TaskResult(
                success=True,
                data=f"Process started with PID: {process.pid}",
                metadata={
                    "pid": process.pid,
                    "command": command,
                    "type": "background"
                }
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error starting process: {e}")
    
    async def _kill_process(self, pid_str: str) -> TaskResult:
        """Kill a process by PID."""
        try:
            pid = int(pid_str.strip())
            
            try:
                process = psutil.Process(pid)
                process_info = {
                    "pid": pid,
                    "name": process.name(),
                    "command": " ".join(process.cmdline())
                }
                
                # Try graceful termination first
                process.terminate()
                
                # Wait a bit for graceful termination
                try:
                    process.wait(timeout=5)
                except psutil.TimeoutExpired:
                    # Force kill if graceful termination fails
                    process.kill()
                    process.wait()
                
                self.process_manager.remove_process(pid)
                
                return TaskResult(
                    success=True,
                    data=f"Process {pid} terminated",
                    metadata=process_info
                )
                
            except psutil.NoSuchProcess:
                return TaskResult(success=False, error=f"Process {pid} not found")
            
        except ValueError:
            return TaskResult(success=False, error="Invalid PID format")
        except Exception as e:
            return TaskResult(success=False, error=f"Error killing process: {e}")
    
    async def _get_process_status(self, pid_str: str) -> TaskResult:
        """Get status of a specific process."""
        try:
            if not pid_str.strip():
                return TaskResult(success=False, error="PID required")
            
            pid = int(pid_str.strip())
            
            try:
                process = psutil.Process(pid)
                
                status_info = {
                    "pid": pid,
                    "name": process.name(),
                    "status": process.status(),
                    "cpu_percent": process.cpu_percent(),
                    "memory_percent": process.memory_percent(),
                    "memory_info": process.memory_info()._asdict(),
                    "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
                    "command": " ".join(process.cmdline()),
                    "cwd": process.cwd() if hasattr(process, 'cwd') else None,
                    "num_threads": process.num_threads()
                }
                
                # Add tracked process info if available
                tracked_info = self.process_manager.get_process_info(pid)
                if tracked_info:
                    status_info.update(tracked_info)
                
                return TaskResult(success=True, data=status_info)
                
            except psutil.NoSuchProcess:
                return TaskResult(success=False, error=f"Process {pid} not found")
            
        except ValueError:
            return TaskResult(success=False, error="Invalid PID format")
        except Exception as e:
            return TaskResult(success=False, error=f"Error getting process status: {e}")
    
    async def _list_processes(self) -> TaskResult:
        """List all tracked processes."""
        try:
            # Clean up finished processes first
            self.process_manager.cleanup_finished_processes()
            
            tracked_processes = self.process_manager.list_processes()
            
            # Enhance with current system information
            enhanced_processes = []
            for proc_info in tracked_processes:
                try:
                    pid = proc_info["pid"]
                    process = psutil.Process(pid)
                    
                    enhanced_info = proc_info.copy()
                    enhanced_info.update({
                        "current_status": process.status(),
                        "cpu_percent": process.cpu_percent(),
                        "memory_percent": process.memory_percent(),
                        "is_running": process.is_running()
                    })
                    enhanced_processes.append(enhanced_info)
                    
                except psutil.NoSuchProcess:
                    # Process no longer exists
                    proc_info["current_status"] = "terminated"
                    proc_info["is_running"] = False
                    enhanced_processes.append(proc_info)
            
            return TaskResult(
                success=True,
                data=enhanced_processes,
                metadata={"total_processes": len(enhanced_processes)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error listing processes: {e}")

    async def _start_interactive_session(self, command: str) -> TaskResult:
        """Start an interactive terminal session using pexpect."""
        try:
            command = command.strip() or "/bin/bash"

            self.log_info(f"Starting interactive session: {command}")

            # Start interactive session
            session = pexpect.spawn(command, cwd=str(self.config.working_directory))
            session.setwinsize(24, 80)  # Set terminal size

            session_id = f"session_{int(time.time())}"
            self.process_manager.interactive_sessions[session_id] = {
                "session": session,
                "command": command,
                "started": datetime.now().isoformat(),
                "pid": session.pid
            }

            # Track the process
            self.process_manager.add_process(session.pid, command, "interactive")

            return TaskResult(
                success=True,
                data=f"Interactive session started: {session_id}",
                metadata={
                    "session_id": session_id,
                    "pid": session.pid,
                    "command": command
                }
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error starting interactive session: {e}")

    async def _send_to_interactive(self, params: str) -> TaskResult:
        """Send command to interactive session. Format: session_id|command"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: session_id|command")

            session_id, command = parts
            session_id = session_id.strip()
            command = command.strip()

            if session_id not in self.process_manager.interactive_sessions:
                return TaskResult(success=False, error=f"Session not found: {session_id}")

            session_info = self.process_manager.interactive_sessions[session_id]
            session = session_info["session"]

            if not session.isalive():
                return TaskResult(success=False, error=f"Session {session_id} is not alive")

            # Send command
            session.sendline(command)

            # Try to read output with timeout
            try:
                session.expect(pexpect.TIMEOUT, timeout=2)
                output = session.before.decode('utf-8', errors='replace') if session.before else ""
            except:
                output = ""

            return TaskResult(
                success=True,
                data={
                    "session_id": session_id,
                    "command": command,
                    "output": output
                },
                metadata={"session_id": session_id}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error sending to interactive session: {e}")

    async def _stop_interactive_session(self, session_id: str) -> TaskResult:
        """Stop an interactive session."""
        try:
            session_id = session_id.strip()

            if session_id not in self.process_manager.interactive_sessions:
                return TaskResult(success=False, error=f"Session not found: {session_id}")

            session_info = self.process_manager.interactive_sessions[session_id]
            session = session_info["session"]

            # Close the session
            if session.isalive():
                session.close()

            # Remove from tracking
            pid = session_info["pid"]
            self.process_manager.remove_process(pid)
            del self.process_manager.interactive_sessions[session_id]

            return TaskResult(
                success=True,
                data=f"Interactive session stopped: {session_id}",
                metadata={"session_id": session_id}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error stopping interactive session: {e}")

    async def _get_system_info(self) -> TaskResult:
        """Get system information."""
        try:
            import platform

            # Get CPU information
            cpu_info = {
                "physical_cores": psutil.cpu_count(logical=False),
                "total_cores": psutil.cpu_count(logical=True),
                "cpu_percent": psutil.cpu_percent(interval=1),
                "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            }

            # Get memory information
            memory = psutil.virtual_memory()
            memory_info = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free
            }

            # Get disk information
            disk = psutil.disk_usage('/')
            disk_info = {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            }

            # Get network information
            network = psutil.net_io_counters()
            network_info = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }

            system_info = {
                "platform": {
                    "system": platform.system(),
                    "node": platform.node(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "network": network_info,
                "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                "current_time": datetime.now().isoformat()
            }

            return TaskResult(success=True, data=system_info)

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting system info: {e}")

    async def _change_directory(self, path: str) -> TaskResult:
        """Change working directory."""
        try:
            path = path.strip()
            if not path:
                path = str(Path.home())

            new_path = Path(path).resolve()

            if not new_path.exists():
                return TaskResult(success=False, error=f"Directory not found: {path}")

            if not new_path.is_dir():
                return TaskResult(success=False, error=f"Not a directory: {path}")

            # Update config working directory
            self.config.working_directory = new_path

            return TaskResult(
                success=True,
                data=f"Changed directory to: {new_path}",
                metadata={"old_path": str(Path.cwd()), "new_path": str(new_path)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error changing directory: {e}")

    async def _get_environment(self, var_name: str = "") -> TaskResult:
        """Get environment variable(s)."""
        try:
            if var_name.strip():
                # Get specific variable
                value = os.environ.get(var_name.strip())
                if value is None:
                    return TaskResult(success=False, error=f"Environment variable not found: {var_name}")

                return TaskResult(
                    success=True,
                    data={var_name.strip(): value},
                    metadata={"variable": var_name.strip()}
                )
            else:
                # Get all environment variables
                env_vars = dict(os.environ)
                return TaskResult(
                    success=True,
                    data=env_vars,
                    metadata={"total_variables": len(env_vars)}
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting environment: {e}")

    async def _set_environment(self, params: str) -> TaskResult:
        """Set environment variable. Format: var_name|value"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: var_name|value")

            var_name, value = parts
            var_name = var_name.strip()
            value = value.strip()

            if not var_name:
                return TaskResult(success=False, error="Variable name cannot be empty")

            # Set environment variable
            os.environ[var_name] = value

            return TaskResult(
                success=True,
                data=f"Environment variable set: {var_name}={value}",
                metadata={"variable": var_name, "value": value}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error setting environment variable: {e}")

    async def shutdown(self) -> None:
        """Shutdown the terminal agent."""
        # Stop all interactive sessions
        for session_id, session_info in list(self.process_manager.interactive_sessions.items()):
            try:
                session = session_info["session"]
                if session.isalive():
                    session.close()
                self.log_info(f"Stopped interactive session: {session_id}")
            except Exception as e:
                self.log_error(f"Error stopping session {session_id}: {e}")

        self.process_manager.interactive_sessions.clear()

        # Clean up tracked processes
        self.process_manager.cleanup_finished_processes()

        self.log_info("Terminal agent shutdown complete")

    # Enhanced terminal operations
    async def _run_in_terminal(self, command: str) -> TaskResult:
        """Run command in terminal with enhanced output capture."""
        return await self._execute_command(command)

    async def _get_terminal_output(self, params: str) -> TaskResult:
        """Get recent terminal output. Format: lines_count"""
        try:
            lines_count = int(params.strip()) if params.strip().isdigit() else 50

            # This would typically read from terminal buffer
            # For now, return a placeholder
            return TaskResult(
                success=True,
                data=f"Terminal output (last {lines_count} lines) - Feature requires terminal integration",
                metadata={"lines_requested": lines_count}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting terminal output: {e}")

    async def _get_terminal_last_command(self, params: str) -> TaskResult:
        """Get the last executed command."""
        # This would typically access command history
        return TaskResult(
            success=True,
            data="Last command - Feature requires terminal integration",
            metadata={"feature": "terminal_history"}
        )

    async def _get_terminal_selection(self, params: str) -> TaskResult:
        """Get currently selected text in terminal."""
        # This would typically access terminal selection
        return TaskResult(
            success=True,
            data="Terminal selection - Feature requires terminal integration",
            metadata={"feature": "terminal_selection"}
        )

    async def _get_task_output(self, task_id: str) -> TaskResult:
        """Get output from a specific task."""
        try:
            task_id = task_id.strip()
            if not task_id:
                return TaskResult(success=False, error="Task ID is required")

            # Look up task in process manager
            for pid, process_info in self.process_manager.processes.items():
                if str(pid) == task_id or process_info.get("task_id") == task_id:
                    return TaskResult(
                        success=True,
                        data=process_info,
                        metadata={"task_id": task_id, "pid": pid}
                    )

            return TaskResult(success=False, error=f"Task {task_id} not found")

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting task output: {e}")

    async def _create_and_run_task(self, params: str) -> TaskResult:
        """Create and run a task. Format: task_name|command"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: task_name|command")

            task_name, command = parts
            task_name = task_name.strip()
            command = command.strip()

            # Execute the command
            result = await self._execute_command(command)

            if result.success:
                # Store task information
                task_info = {
                    "name": task_name,
                    "command": command,
                    "result": result.data,
                    "timestamp": datetime.now().isoformat()
                }

                return TaskResult(
                    success=True,
                    data=task_info,
                    metadata={"task_name": task_name}
                )
            else:
                return result

        except Exception as e:
            return TaskResult(success=False, error=f"Error creating and running task: {e}")

    async def _install_python_packages(self, packages: str) -> TaskResult:
        """Install Python packages using pip. Format: package1,package2,package3"""
        try:
            package_list = [pkg.strip() for pkg in packages.split(",") if pkg.strip()]

            if not package_list:
                return TaskResult(success=False, error="No packages specified")

            # Install packages
            command = f"pip install {' '.join(package_list)}"
            result = await self._execute_command(command)

            return TaskResult(
                success=result.success,
                data=result.data if result.success else result.error,
                metadata={"packages": package_list, "command": command}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error installing packages: {e}")

    async def _configure_python_environment(self, params: str) -> TaskResult:
        """Configure Python environment. Format: action|parameters"""
        try:
            parts = params.split("|", 1)
            action = parts[0].strip().lower()
            parameters = parts[1].strip() if len(parts) > 1 else ""

            if action == "create_venv":
                venv_name = parameters or "venv"
                command = f"python -m venv {venv_name}"

            elif action == "activate_venv":
                venv_name = parameters or "venv"
                if sys.platform == "win32":
                    command = f"{venv_name}\\Scripts\\activate"
                else:
                    command = f"source {venv_name}/bin/activate"

            elif action == "install_requirements":
                req_file = parameters or "requirements.txt"
                command = f"pip install -r {req_file}"

            elif action == "freeze_requirements":
                output_file = parameters or "requirements.txt"
                command = f"pip freeze > {output_file}"

            else:
                return TaskResult(success=False, error=f"Unknown action: {action}")

            result = await self._execute_command(command)

            return TaskResult(
                success=result.success,
                data=result.data if result.success else result.error,
                metadata={"action": action, "command": command}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error configuring Python environment: {e}")

    # Testing and debugging methods
    async def _test_search(self, params: str) -> TaskResult:
        """Search for test files and functions. Format: directory|pattern"""
        try:
            parts = params.split("|", 1)
            directory = Path(parts[0].strip()) if parts[0].strip() else Path.cwd()
            pattern = parts[1].strip() if len(parts) > 1 else "test_*.py"

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            test_files = []
            test_functions = []

            # Find test files
            for test_file in directory.rglob(pattern):
                test_files.append(str(test_file))

                # Extract test functions
                try:
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    import ast
                    tree = ast.parse(content)

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                            test_functions.append({
                                "file": str(test_file),
                                "function": node.name,
                                "line": node.lineno
                            })

                except Exception:
                    continue

            return TaskResult(
                success=True,
                data={
                    "test_files": test_files,
                    "test_functions": test_functions,
                    "total_files": len(test_files),
                    "total_functions": len(test_functions)
                },
                metadata={"directory": str(directory), "pattern": pattern}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error searching tests: {e}")

    async def _test_failure(self, params: str) -> TaskResult:
        """Analyze test failures. Format: test_output"""
        try:
            test_output = params.strip()

            if not test_output:
                return TaskResult(success=False, error="Test output is required")

            # Parse test output for failures
            failures = []
            lines = test_output.split('\n')

            current_failure = None
            for line in lines:
                if 'FAILED' in line or 'ERROR' in line:
                    if current_failure:
                        failures.append(current_failure)
                    current_failure = {
                        "test": line.strip(),
                        "details": []
                    }
                elif current_failure and line.strip():
                    current_failure["details"].append(line.strip())

            if current_failure:
                failures.append(current_failure)

            return TaskResult(
                success=True,
                data={
                    "failures": failures,
                    "failure_count": len(failures),
                    "analysis": "Test failure analysis completed"
                },
                metadata={"output_length": len(test_output)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error analyzing test failures: {e}")

    async def _run_tests(self, params: str) -> TaskResult:
        """Run tests with specified framework. Format: framework|directory|options"""
        try:
            parts = params.split("|", 2)
            framework = parts[0].strip().lower() if parts else "pytest"
            directory = parts[1].strip() if len(parts) > 1 else "."
            options = parts[2].strip() if len(parts) > 2 else ""

            # Build test command based on framework
            if framework == "pytest":
                command = f"pytest {directory} {options}"
            elif framework == "unittest":
                command = f"python -m unittest discover {directory} {options}"
            elif framework == "nose":
                command = f"nosetests {directory} {options}"
            elif framework == "tox":
                command = f"tox {options}"
            else:
                return TaskResult(success=False, error=f"Unsupported test framework: {framework}")

            # Run the tests
            result = await self._execute_command(command)

            return TaskResult(
                success=result.success,
                data=result.data,
                metadata={
                    "framework": framework,
                    "directory": directory,
                    "command": command
                }
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error running tests: {e}")

    async def _autonomous_debugger(self, params: str) -> TaskResult:
        """Autonomous debugging assistance. Format: error_message|file_path"""
        try:
            parts = params.split("|", 1)
            error_message = parts[0].strip()
            file_path = parts[1].strip() if len(parts) > 1 else ""

            if not error_message:
                return TaskResult(success=False, error="Error message is required")

            debug_suggestions = []

            # Analyze error message
            if "NameError" in error_message:
                debug_suggestions.append("Check for undefined variables or typos in variable names")
            elif "ImportError" in error_message or "ModuleNotFoundError" in error_message:
                debug_suggestions.append("Check if the module is installed and the import path is correct")
            elif "SyntaxError" in error_message:
                debug_suggestions.append("Check for syntax errors like missing colons, parentheses, or indentation")
            elif "TypeError" in error_message:
                debug_suggestions.append("Check for type mismatches or incorrect function arguments")
            elif "AttributeError" in error_message:
                debug_suggestions.append("Check if the object has the attribute or method being accessed")
            elif "IndexError" in error_message:
                debug_suggestions.append("Check array/list bounds and ensure indices are valid")
            elif "KeyError" in error_message:
                debug_suggestions.append("Check if the dictionary key exists or use .get() method")

            # File-specific analysis
            if file_path and Path(file_path).exists():
                debug_suggestions.append(f"Review the code in {file_path} around the error location")

                # Basic static analysis
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Check for common issues
                    if "import" in error_message and "import " in content:
                        debug_suggestions.append("Check import statements and module availability")

                    if content.count('(') != content.count(')'):
                        debug_suggestions.append("Check for unmatched parentheses")

                    if content.count('[') != content.count(']'):
                        debug_suggestions.append("Check for unmatched square brackets")

                except Exception:
                    pass

            return TaskResult(
                success=True,
                data={
                    "error_message": error_message,
                    "file_path": file_path,
                    "suggestions": debug_suggestions,
                    "next_steps": [
                        "Review the error message carefully",
                        "Check the suggested areas",
                        "Use print statements or debugger for detailed analysis",
                        "Test with minimal examples"
                    ]
                },
                metadata={"suggestions_count": len(debug_suggestions)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in autonomous debugging: {e}")

    async def _lint_check(self, params: str) -> TaskResult:
        """Run linting tools. Format: tool|directory|options"""
        try:
            parts = params.split("|", 2)
            tool = parts[0].strip().lower() if parts else "flake8"
            directory = parts[1].strip() if len(parts) > 1 else "."
            options = parts[2].strip() if len(parts) > 2 else ""

            # Build lint command based on tool
            if tool == "flake8":
                command = f"flake8 {directory} {options}"
            elif tool == "pylint":
                command = f"pylint {directory} {options}"
            elif tool == "black":
                command = f"black --check {directory} {options}"
            elif tool == "isort":
                command = f"isort --check-only {directory} {options}"
            elif tool == "mypy":
                command = f"mypy {directory} {options}"
            else:
                return TaskResult(success=False, error=f"Unsupported linting tool: {tool}")

            # Run the linter
            result = await self._execute_command(command)

            return TaskResult(
                success=True,  # Always return success, even if linter finds issues
                data=result.data,
                metadata={
                    "tool": tool,
                    "directory": directory,
                    "command": command,
                    "linter_success": result.success
                }
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error running lint check: {e}")

    async def _self_repair(self, params: str) -> TaskResult:
        """Attempt to automatically fix common issues. Format: issue_type|file_path"""
        try:
            parts = params.split("|", 1)
            issue_type = parts[0].strip().lower()
            file_path = parts[1].strip() if len(parts) > 1 else ""

            repair_actions = []

            if issue_type == "imports":
                # Fix import issues
                if file_path and Path(file_path).exists():
                    # Run isort to fix import order
                    result = await self._execute_command(f"isort {file_path}")
                    if result.success:
                        repair_actions.append("Fixed import order with isort")

            elif issue_type == "formatting":
                # Fix code formatting
                if file_path and Path(file_path).exists():
                    # Run black to fix formatting
                    result = await self._execute_command(f"black {file_path}")
                    if result.success:
                        repair_actions.append("Fixed code formatting with black")

            elif issue_type == "syntax":
                # Basic syntax repair attempts
                if file_path and Path(file_path).exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Check for common syntax issues
                        lines = content.splitlines()
                        fixed_lines = []

                        for line in lines:
                            # Fix common issues
                            if line.strip().endswith(',') and ':' in line and not line.strip().endswith(':'):
                                # Possible missing colon after comma
                                line = line.rstrip(',') + ':'
                                repair_actions.append("Added missing colon")

                            fixed_lines.append(line)

                        if repair_actions:
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.write('\n'.join(fixed_lines))

                    except Exception:
                        pass

            elif issue_type == "dependencies":
                # Try to install missing dependencies
                result = await self._execute_command("pip install -r requirements.txt")
                if result.success:
                    repair_actions.append("Installed missing dependencies")

            return TaskResult(
                success=True,
                data={
                    "issue_type": issue_type,
                    "file_path": file_path,
                    "repair_actions": repair_actions,
                    "repairs_made": len(repair_actions)
                },
                metadata={"issue_type": issue_type}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in self repair: {e}")

    async def _code_linting_static_analysis(self, params: str) -> TaskResult:
        """Comprehensive code analysis. Format: directory|tools"""
        try:
            parts = params.split("|", 1)
            directory = parts[0].strip() if parts[0].strip() else "."
            tools = parts[1].strip().split(",") if len(parts) > 1 else ["flake8", "pylint", "mypy"]

            analysis_results = {}

            for tool in tools:
                tool = tool.strip().lower()

                # Run each analysis tool
                if tool == "flake8":
                    result = await self._execute_command(f"flake8 {directory}")
                elif tool == "pylint":
                    result = await self._execute_command(f"pylint {directory}")
                elif tool == "mypy":
                    result = await self._execute_command(f"mypy {directory}")
                elif tool == "bandit":
                    result = await self._execute_command(f"bandit -r {directory}")
                elif tool == "safety":
                    result = await self._execute_command("safety check")
                else:
                    continue

                analysis_results[tool] = {
                    "success": result.success,
                    "output": result.data.get("stdout", "") if result.success else result.error,
                    "errors": result.data.get("stderr", "") if result.success else ""
                }

            # Summarize results
            total_tools = len(analysis_results)
            successful_tools = sum(1 for r in analysis_results.values() if r["success"])

            return TaskResult(
                success=True,
                data={
                    "directory": directory,
                    "tools_run": list(analysis_results.keys()),
                    "results": analysis_results,
                    "summary": {
                        "total_tools": total_tools,
                        "successful_tools": successful_tools,
                        "success_rate": f"{successful_tools/total_tools*100:.1f}%" if total_tools > 0 else "0%"
                    }
                },
                metadata={"directory": directory, "tools_count": total_tools}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in code analysis: {e}")
