"""
Performance Agent - Specialized in performance optimization.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class PerformanceAgent(BaseAgent):
    """Specialized agent for performance optimization."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("PerformanceAgent", [])
        self.gemini_client = gemini_client
    
    async def optimize_performance(self, code: str) -> TaskResult:
        """Analyze and optimize code performance."""
        prompt = f"""
        As a senior performance engineer, optimize this code:
        
        Code: {code}
        
        Provide:
        1. Performance bottleneck analysis
        2. Optimization recommendations
        3. Memory usage improvements
        4. Algorithm complexity analysis
        5. Scalability enhancements
        """
        
        return await self.gemini_client.generate_response(prompt)
