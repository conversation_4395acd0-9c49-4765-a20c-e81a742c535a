"""
Tester Agent - Specialized in testing and quality assurance.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class TesterAgent(BaseAgent):
    """Specialized agent for testing and quality assurance."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("TesterAgent", [])
        self.gemini_client = gemini_client
    
    async def generate_tests(self, code: str, test_type: str = "unit") -> TaskResult:
        """Generate comprehensive tests for code."""
        prompt = f"""
        As a senior QA engineer, create comprehensive tests for this code:
        
        Code: {code}
        Test Type: {test_type}
        
        Provide:
        1. Unit tests with edge cases
        2. Integration tests
        3. Performance tests
        4. Error condition tests
        5. Test coverage analysis
        """
        
        return await self.gemini_client.generate_response(prompt)
