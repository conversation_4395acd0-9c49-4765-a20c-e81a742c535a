"""
Web scraping and fetching capability for the Augment Coding Assistant.
"""

import asyncio
import aiohttp
import requests
from bs4 import <PERSON>Soup
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse, parse_qs
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import json
import time
import re
from datetime import datetime
import mimetypes

from ..core.base import BaseCapability, TaskResult
from ..core.config import WebConfig


class WebAgent(BaseCapability):
    """
    Comprehensive web capabilities including scraping, fetching, and downloading.
    Supports both static and dynamic content.
    """
    
    def __init__(self, config: WebConfig):
        super().__init__("web", "Web scraping, fetching, and downloading")
        self.config = config
        self.session = None
        self.playwright_browser = None
        self.request_count = 0
        self.last_request_time = 0
    
    async def execute(self, task: str, **kwargs) -> TaskResult:
        """Execute a web task."""
        parameters = kwargs.get("parameters", "")
        
        try:
            if task == "fetch_url":
                return await self._fetch_url(parameters)
            elif task == "scrape_page":
                return await self._scrape_page(parameters)
            elif task == "download_file":
                return await self._download_file(parameters)
            elif task == "search_content":
                return await self._search_content(parameters)
            elif task == "extract_links":
                return await self._extract_links(parameters)
            elif task == "get_page_info":
                return await self._get_page_info(parameters)
            elif task == "scrape_dynamic":
                return await self._scrape_dynamic_page(parameters)
            elif task == "submit_form":
                return await self._submit_form(parameters)
            elif task == "check_status":
                return await self._check_url_status(parameters)
            elif task == "extract_images":
                return await self._extract_images(parameters)
            elif task == "get_headers":
                return await self._get_headers(parameters)
            elif task == "follow_redirects":
                return await self._follow_redirects(parameters)
            # Enhanced web operations
            elif task == "web_search":
                return await self._web_search(parameters)
            elif task == "web_fetch":
                return await self._fetch_url(parameters)  # Alias
            elif task == "web_scrape":
                return await self._scrape_page(parameters)  # Alias
            elif task == "web_download":
                return await self._download_file(parameters)  # Alias
            elif task == "api_request":
                return await self._api_request(parameters)
            elif task == "webhook_handler":
                return await self._webhook_handler(parameters)
            elif task == "url_monitor":
                return await self._url_monitor(parameters)
            elif task == "sitemap_crawler":
                return await self._sitemap_crawler(parameters)
            elif task == "performance_test":
                return await self._performance_test(parameters)
            elif task == "accessibility_check":
                return await self._accessibility_check(parameters)
            elif task == "seo_analysis":
                return await self._seo_analysis(parameters)
            else:
                return TaskResult(success=False, error=f"Unknown task: {task}")
                
        except Exception as e:
            self.log_error(f"Error executing {task}: {e}")
            return TaskResult(success=False, error=str(e))
    
    def get_available_actions(self) -> List[str]:
        """Get list of available web actions."""
        return [
            # Basic web operations
            "fetch_url", "scrape_page", "download_file", "search_content",
            "extract_links", "get_page_info", "scrape_dynamic", "submit_form",
            "check_status", "extract_images", "get_headers", "follow_redirects",
            # Enhanced web operations
            "web_search", "web_fetch", "web_scrape", "web_download",
            "api_request", "webhook_handler", "url_monitor", "sitemap_crawler",
            "performance_test", "accessibility_check", "seo_analysis"
        ]
    
    async def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure we have an active aiohttp session."""
        if self.session is None or self.session.closed:
            headers = {
                'User-Agent': self.config.user_agent
            }
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        return self.session
    
    async def _rate_limit(self) -> None:
        """Apply rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.config.delay_between_requests:
            await asyncio.sleep(self.config.delay_between_requests - time_since_last)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    async def _fetch_url(self, url: str) -> TaskResult:
        """Fetch content from a URL."""
        try:
            url = url.strip()
            if not url:
                return TaskResult(success=False, error="URL is required")
            
            await self._rate_limit()
            session = await self._ensure_session()
            
            self.log_info(f"Fetching URL: {url}")
            
            async with session.get(url) as response:
                content = await response.text()
                
                result_data = {
                    "url": url,
                    "status_code": response.status,
                    "content": content,
                    "headers": dict(response.headers),
                    "content_type": response.headers.get('content-type', ''),
                    "content_length": len(content)
                }
                
                success = 200 <= response.status < 300
                
                return TaskResult(
                    success=success,
                    data=result_data,
                    metadata={
                        "url": url,
                        "status_code": response.status,
                        "content_length": len(content)
                    }
                )
                
        except Exception as e:
            return TaskResult(success=False, error=f"Error fetching URL: {e}")
    
    async def _scrape_page(self, params: str) -> TaskResult:
        """Scrape structured data from a page. Format: url|selector_type|selector"""
        try:
            parts = params.split("|", 2)
            if len(parts) < 1:
                return TaskResult(success=False, error="URL is required")
            
            url = parts[0].strip()
            selector_type = parts[1].strip() if len(parts) > 1 else "all"
            selector = parts[2].strip() if len(parts) > 2 else None
            
            # Fetch the page
            fetch_result = await self._fetch_url(url)
            if not fetch_result.success:
                return fetch_result
            
            content = fetch_result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')
            
            scraped_data = {}
            
            if selector_type == "all" or not selector:
                # Extract common elements
                scraped_data = {
                    "title": soup.title.string if soup.title else None,
                    "meta_description": self._get_meta_content(soup, "description"),
                    "meta_keywords": self._get_meta_content(soup, "keywords"),
                    "headings": {
                        f"h{i}": [h.get_text().strip() for h in soup.find_all(f"h{i}")]
                        for i in range(1, 7)
                    },
                    "links": [
                        {"text": a.get_text().strip(), "href": a.get("href")}
                        for a in soup.find_all("a", href=True)
                    ],
                    "images": [
                        {"alt": img.get("alt", ""), "src": img.get("src")}
                        for img in soup.find_all("img", src=True)
                    ],
                    "paragraphs": [p.get_text().strip() for p in soup.find_all("p")],
                    "text_content": soup.get_text().strip()
                }
            elif selector_type == "css" and selector:
                # CSS selector
                elements = soup.select(selector)
                scraped_data = [
                    {
                        "text": elem.get_text().strip(),
                        "html": str(elem),
                        "attributes": elem.attrs
                    }
                    for elem in elements
                ]
            elif selector_type == "xpath" and selector:
                # XPath not directly supported by BeautifulSoup
                return TaskResult(success=False, error="XPath selectors require dynamic scraping")
            elif selector_type == "tag" and selector:
                # Tag selector
                elements = soup.find_all(selector)
                scraped_data = [
                    {
                        "text": elem.get_text().strip(),
                        "html": str(elem),
                        "attributes": elem.attrs
                    }
                    for elem in elements
                ]
            
            return TaskResult(
                success=True,
                data=scraped_data,
                metadata={
                    "url": url,
                    "selector_type": selector_type,
                    "selector": selector,
                    "elements_found": len(scraped_data) if isinstance(scraped_data, list) else 1
                }
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error scraping page: {e}")
    
    def _get_meta_content(self, soup: BeautifulSoup, name: str) -> Optional[str]:
        """Extract meta tag content."""
        meta = soup.find("meta", attrs={"name": name}) or soup.find("meta", attrs={"property": f"og:{name}"})
        return meta.get("content") if meta else None
    
    async def _download_file(self, params: str) -> TaskResult:
        """Download a file. Format: url|local_path"""
        try:
            parts = params.split("|", 1)
            if len(parts) < 1:
                return TaskResult(success=False, error="URL is required")
            
            url = parts[0].strip()
            local_path = parts[1].strip() if len(parts) > 1 else None
            
            # Determine local path if not provided
            if not local_path:
                parsed_url = urlparse(url)
                filename = Path(parsed_url.path).name or "downloaded_file"
                local_path = str(Path.cwd() / filename)
            
            await self._rate_limit()
            session = await self._ensure_session()
            
            self.log_info(f"Downloading: {url} -> {local_path}")
            
            async with session.get(url) as response:
                if response.status != 200:
                    return TaskResult(
                        success=False,
                        error=f"HTTP {response.status}: {response.reason}"
                    )
                
                # Create directory if needed
                Path(local_path).parent.mkdir(parents=True, exist_ok=True)
                
                # Download file
                with open(local_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        f.write(chunk)
                
                file_size = Path(local_path).stat().st_size
                
                return TaskResult(
                    success=True,
                    data=f"Downloaded: {url} -> {local_path}",
                    metadata={
                        "url": url,
                        "local_path": local_path,
                        "file_size": file_size,
                        "content_type": response.headers.get('content-type', '')
                    }
                )
                
        except Exception as e:
            return TaskResult(success=False, error=f"Error downloading file: {e}")
    
    async def _search_content(self, params: str) -> TaskResult:
        """Search for content within a page. Format: url|search_term|context_lines"""
        try:
            parts = params.split("|", 2)
            if len(parts) < 2:
                return TaskResult(success=False, error="URL and search term are required")
            
            url = parts[0].strip()
            search_term = parts[1].strip()
            context_lines = int(parts[2].strip()) if len(parts) > 2 and parts[2].strip().isdigit() else 2
            
            # Fetch the page
            fetch_result = await self._fetch_url(url)
            if not fetch_result.success:
                return fetch_result
            
            content = fetch_result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')
            text_content = soup.get_text()
            
            # Search for the term
            lines = text_content.split('\n')
            matches = []
            
            for i, line in enumerate(lines):
                if search_term.lower() in line.lower():
                    # Get context
                    start = max(0, i - context_lines)
                    end = min(len(lines), i + context_lines + 1)
                    context = lines[start:end]
                    
                    matches.append({
                        "line_number": i + 1,
                        "line": line.strip(),
                        "context": [l.strip() for l in context],
                        "context_start": start + 1,
                        "context_end": end
                    })
            
            return TaskResult(
                success=True,
                data=matches,
                metadata={
                    "url": url,
                    "search_term": search_term,
                    "total_matches": len(matches),
                    "context_lines": context_lines
                }
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error searching content: {e}")

    async def _extract_links(self, url: str) -> TaskResult:
        """Extract all links from a page."""
        try:
            url = url.strip()

            # Fetch the page
            fetch_result = await self._fetch_url(url)
            if not fetch_result.success:
                return fetch_result

            content = fetch_result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')

            links = []
            for a in soup.find_all("a", href=True):
                href = a.get("href")
                text = a.get_text().strip()

                # Convert relative URLs to absolute
                absolute_url = urljoin(url, href)

                links.append({
                    "text": text,
                    "href": href,
                    "absolute_url": absolute_url,
                    "is_external": urlparse(absolute_url).netloc != urlparse(url).netloc
                })

            # Categorize links
            internal_links = [l for l in links if not l["is_external"]]
            external_links = [l for l in links if l["is_external"]]

            return TaskResult(
                success=True,
                data={
                    "all_links": links,
                    "internal_links": internal_links,
                    "external_links": external_links,
                    "total_links": len(links),
                    "internal_count": len(internal_links),
                    "external_count": len(external_links)
                },
                metadata={"url": url, "total_links": len(links)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error extracting links: {e}")

    async def _get_page_info(self, url: str) -> TaskResult:
        """Get comprehensive information about a page."""
        try:
            url = url.strip()

            # Fetch the page
            fetch_result = await self._fetch_url(url)
            if not fetch_result.success:
                return fetch_result

            content = fetch_result.data["content"]
            headers = fetch_result.data["headers"]
            soup = BeautifulSoup(content, 'html.parser')

            # Extract page information
            page_info = {
                "url": url,
                "title": soup.title.string if soup.title else None,
                "meta": {
                    "description": self._get_meta_content(soup, "description"),
                    "keywords": self._get_meta_content(soup, "keywords"),
                    "author": self._get_meta_content(soup, "author"),
                    "viewport": self._get_meta_content(soup, "viewport"),
                },
                "og_tags": {
                    "title": self._get_meta_content(soup, "title"),
                    "description": self._get_meta_content(soup, "description"),
                    "image": self._get_meta_content(soup, "image"),
                    "url": self._get_meta_content(soup, "url"),
                },
                "structure": {
                    "headings_count": {f"h{i}": len(soup.find_all(f"h{i}")) for i in range(1, 7)},
                    "paragraphs_count": len(soup.find_all("p")),
                    "links_count": len(soup.find_all("a", href=True)),
                    "images_count": len(soup.find_all("img", src=True)),
                    "forms_count": len(soup.find_all("form")),
                },
                "technical": {
                    "content_type": headers.get('content-type', ''),
                    "content_length": len(content),
                    "server": headers.get('server', ''),
                    "last_modified": headers.get('last-modified', ''),
                    "etag": headers.get('etag', ''),
                },
                "scripts": [script.get("src") for script in soup.find_all("script", src=True)],
                "stylesheets": [link.get("href") for link in soup.find_all("link", rel="stylesheet")],
                "language": soup.html.get("lang") if soup.html else None,
                "charset": self._extract_charset(soup, headers),
            }

            return TaskResult(success=True, data=page_info, metadata={"url": url})

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting page info: {e}")

    def _extract_charset(self, soup: BeautifulSoup, headers: Dict[str, str]) -> Optional[str]:
        """Extract charset from page."""
        # Try from headers first
        content_type = headers.get('content-type', '')
        if 'charset=' in content_type:
            return content_type.split('charset=')[1].split(';')[0].strip()

        # Try from meta tag
        meta_charset = soup.find("meta", attrs={"charset": True})
        if meta_charset:
            return meta_charset.get("charset")

        # Try from http-equiv meta tag
        meta_http_equiv = soup.find("meta", attrs={"http-equiv": "Content-Type"})
        if meta_http_equiv:
            content = meta_http_equiv.get("content", "")
            if 'charset=' in content:
                return content.split('charset=')[1].split(';')[0].strip()

        return None

    async def _scrape_dynamic_page(self, params: str) -> TaskResult:
        """Scrape dynamic content using Playwright. Format: url|wait_selector|timeout"""
        try:
            parts = params.split("|", 2)
            if len(parts) < 1:
                return TaskResult(success=False, error="URL is required")

            url = parts[0].strip()
            wait_selector = parts[1].strip() if len(parts) > 1 else None
            timeout = int(parts[2].strip()) if len(parts) > 2 and parts[2].strip().isdigit() else 30000

            self.log_info(f"Scraping dynamic page: {url}")

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()

                # Set user agent
                await page.set_extra_http_headers({"User-Agent": self.config.user_agent})

                # Navigate to page
                await page.goto(url, timeout=timeout)

                # Wait for specific selector if provided
                if wait_selector:
                    await page.wait_for_selector(wait_selector, timeout=timeout)
                else:
                    # Wait for network to be idle
                    await page.wait_for_load_state("networkidle")

                # Get page content
                content = await page.content()
                title = await page.title()

                # Extract additional dynamic information
                page_info = {
                    "url": url,
                    "title": title,
                    "content": content,
                    "final_url": page.url,
                    "viewport": await page.viewport_size(),
                }

                # Parse with BeautifulSoup for structured data
                soup = BeautifulSoup(content, 'html.parser')
                page_info["structured_data"] = {
                    "headings": {f"h{i}": [h.get_text().strip() for h in soup.find_all(f"h{i}")] for i in range(1, 7)},
                    "links": [{"text": a.get_text().strip(), "href": a.get("href")} for a in soup.find_all("a", href=True)],
                    "images": [{"alt": img.get("alt", ""), "src": img.get("src")} for img in soup.find_all("img", src=True)],
                    "text_content": soup.get_text().strip()
                }

                await browser.close()

                return TaskResult(
                    success=True,
                    data=page_info,
                    metadata={
                        "url": url,
                        "final_url": page.url,
                        "wait_selector": wait_selector,
                        "timeout": timeout
                    }
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error scraping dynamic page: {e}")

    async def _check_url_status(self, url: str) -> TaskResult:
        """Check the status of a URL."""
        try:
            url = url.strip()

            await self._rate_limit()
            session = await self._ensure_session()

            start_time = time.time()

            async with session.head(url) as response:
                response_time = time.time() - start_time

                status_info = {
                    "url": url,
                    "status_code": response.status,
                    "status_text": response.reason,
                    "response_time": round(response_time * 1000, 2),  # in milliseconds
                    "headers": dict(response.headers),
                    "content_type": response.headers.get('content-type', ''),
                    "content_length": response.headers.get('content-length', ''),
                    "server": response.headers.get('server', ''),
                    "last_modified": response.headers.get('last-modified', ''),
                    "is_accessible": 200 <= response.status < 300,
                    "is_redirect": 300 <= response.status < 400,
                    "is_client_error": 400 <= response.status < 500,
                    "is_server_error": 500 <= response.status < 600,
                }

                return TaskResult(
                    success=True,
                    data=status_info,
                    metadata={"url": url, "status_code": response.status}
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error checking URL status: {e}")

    async def _extract_images(self, url: str) -> TaskResult:
        """Extract all images from a page."""
        try:
            url = url.strip()

            # Fetch the page
            fetch_result = await self._fetch_url(url)
            if not fetch_result.success:
                return fetch_result

            content = fetch_result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')

            images = []
            for img in soup.find_all("img"):
                src = img.get("src")
                if src:
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(url, src)

                    images.append({
                        "src": src,
                        "absolute_url": absolute_url,
                        "alt": img.get("alt", ""),
                        "title": img.get("title", ""),
                        "width": img.get("width", ""),
                        "height": img.get("height", ""),
                        "class": img.get("class", []),
                    })

            return TaskResult(
                success=True,
                data=images,
                metadata={"url": url, "total_images": len(images)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error extracting images: {e}")

    async def _get_headers(self, url: str) -> TaskResult:
        """Get HTTP headers for a URL."""
        try:
            url = url.strip()

            await self._rate_limit()
            session = await self._ensure_session()

            async with session.head(url) as response:
                headers_info = {
                    "url": url,
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "cookies": {cookie.key: cookie.value for cookie in response.cookies}
                }

                return TaskResult(
                    success=True,
                    data=headers_info,
                    metadata={"url": url, "status_code": response.status}
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting headers: {e}")

    async def _follow_redirects(self, url: str) -> TaskResult:
        """Follow redirects and show the chain."""
        try:
            url = url.strip()

            await self._rate_limit()

            # Use requests for redirect following (easier than aiohttp for this)
            response = requests.head(url, allow_redirects=True, timeout=self.config.timeout)

            redirect_chain = []
            if hasattr(response, 'history'):
                for resp in response.history:
                    redirect_chain.append({
                        "url": resp.url,
                        "status_code": resp.status_code,
                        "location": resp.headers.get('location', '')
                    })

            # Add final URL
            redirect_chain.append({
                "url": response.url,
                "status_code": response.status_code,
                "location": None
            })

            redirect_info = {
                "original_url": url,
                "final_url": response.url,
                "redirect_count": len(redirect_chain) - 1,
                "redirect_chain": redirect_chain,
                "final_status": response.status_code
            }

            return TaskResult(
                success=True,
                data=redirect_info,
                metadata={"original_url": url, "final_url": response.url}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error following redirects: {e}")

    async def shutdown(self) -> None:
        """Shutdown the web agent."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.log_info("HTTP session closed")

        if self.playwright_browser:
            await self.playwright_browser.close()
            self.log_info("Playwright browser closed")

        self.log_info("Web agent shutdown complete")

    # Enhanced web operations
    async def _web_search(self, params: str) -> TaskResult:
        """Perform web search using search engines. Format: query|engine|count"""
        try:
            parts = params.split("|", 2)
            query = parts[0].strip()
            engine = parts[1].strip().lower() if len(parts) > 1 else "duckduckgo"
            count = int(parts[2].strip()) if len(parts) > 2 and parts[2].strip().isdigit() else 10

            if not query:
                return TaskResult(success=False, error="Search query is required")

            # Simple web search implementation
            # In a real implementation, you'd use search APIs
            search_results = []

            if engine == "duckduckgo":
                # Simulate DuckDuckGo search
                search_url = f"https://duckduckgo.com/html/?q={query.replace(' ', '+')}"
                result = await self._fetch_url(search_url)

                if result.success:
                    soup = BeautifulSoup(result.data["content"], 'html.parser')

                    # Extract search results
                    for i, result_div in enumerate(soup.find_all('div', class_='result'), 1):
                        if i > count:
                            break

                        title_elem = result_div.find('a', class_='result__a')
                        snippet_elem = result_div.find('a', class_='result__snippet')

                        if title_elem:
                            search_results.append({
                                "title": title_elem.get_text().strip(),
                                "url": title_elem.get('href', ''),
                                "snippet": snippet_elem.get_text().strip() if snippet_elem else ""
                            })

            return TaskResult(
                success=True,
                data=search_results,
                metadata={"query": query, "engine": engine, "count": len(search_results)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in web search: {e}")

    async def _api_request(self, params: str) -> TaskResult:
        """Make API requests. Format: method|url|headers|body"""
        try:
            parts = params.split("|", 3)
            method = parts[0].strip().upper()
            url = parts[1].strip()
            headers_str = parts[2].strip() if len(parts) > 2 else ""
            body = parts[3].strip() if len(parts) > 3 else ""

            if not url:
                return TaskResult(success=False, error="URL is required")

            # Parse headers
            headers = {}
            if headers_str:
                try:
                    import json
                    headers = json.loads(headers_str)
                except:
                    # Try simple key:value format
                    for header in headers_str.split(','):
                        if ':' in header:
                            key, value = header.split(':', 1)
                            headers[key.strip()] = value.strip()

            await self._rate_limit()
            session = await self._ensure_session()

            # Make the request
            kwargs = {"headers": headers}
            if body and method in ["POST", "PUT", "PATCH"]:
                kwargs["data"] = body

            async with session.request(method, url, **kwargs) as response:
                content = await response.text()

                result_data = {
                    "method": method,
                    "url": url,
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content": content,
                    "success": 200 <= response.status < 300
                }

                return TaskResult(
                    success=True,
                    data=result_data,
                    metadata={"method": method, "status": response.status}
                )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in API request: {e}")

    async def _webhook_handler(self, params: str) -> TaskResult:
        """Handle webhook data. Format: webhook_data"""
        try:
            webhook_data = params.strip()

            if not webhook_data:
                return TaskResult(success=False, error="Webhook data is required")

            # Parse webhook data
            try:
                import json
                data = json.loads(webhook_data)
            except:
                data = {"raw_data": webhook_data}

            # Process webhook
            processed_data = {
                "timestamp": datetime.now().isoformat(),
                "data": data,
                "processed": True
            }

            return TaskResult(
                success=True,
                data=processed_data,
                metadata={"webhook_processed": True}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error handling webhook: {e}")

    async def _url_monitor(self, params: str) -> TaskResult:
        """Monitor URL for changes. Format: url|interval|duration"""
        try:
            parts = params.split("|", 2)
            url = parts[0].strip()
            interval = int(parts[1].strip()) if len(parts) > 1 and parts[1].strip().isdigit() else 60
            duration = int(parts[2].strip()) if len(parts) > 2 and parts[2].strip().isdigit() else 300

            if not url:
                return TaskResult(success=False, error="URL is required")

            # Simple monitoring implementation
            monitoring_data = {
                "url": url,
                "interval": interval,
                "duration": duration,
                "status": "monitoring_started",
                "note": "This is a simplified implementation. Full monitoring would require background tasks."
            }

            # Get initial state
            initial_result = await self._fetch_url(url)
            if initial_result.success:
                monitoring_data["initial_status"] = initial_result.data["status_code"]
                monitoring_data["initial_content_length"] = len(initial_result.data["content"])

            return TaskResult(
                success=True,
                data=monitoring_data,
                metadata={"url": url, "monitoring": True}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in URL monitoring: {e}")

    async def _sitemap_crawler(self, params: str) -> TaskResult:
        """Crawl website sitemap. Format: base_url|max_pages"""
        try:
            parts = params.split("|", 1)
            base_url = parts[0].strip()
            max_pages = int(parts[1].strip()) if len(parts) > 1 and parts[1].strip().isdigit() else 50

            if not base_url:
                return TaskResult(success=False, error="Base URL is required")

            # Try to find sitemap
            sitemap_urls = [
                f"{base_url}/sitemap.xml",
                f"{base_url}/sitemap_index.xml",
                f"{base_url}/robots.txt"
            ]

            found_urls = []

            for sitemap_url in sitemap_urls:
                try:
                    result = await self._fetch_url(sitemap_url)
                    if result.success:
                        content = result.data["content"]

                        if sitemap_url.endswith('.xml'):
                            # Parse XML sitemap
                            soup = BeautifulSoup(content, 'xml')
                            for loc in soup.find_all('loc'):
                                if len(found_urls) < max_pages:
                                    found_urls.append(loc.get_text().strip())

                        elif sitemap_url.endswith('robots.txt'):
                            # Parse robots.txt for sitemap references
                            for line in content.split('\n'):
                                if line.strip().lower().startswith('sitemap:'):
                                    sitemap_ref = line.split(':', 1)[1].strip()
                                    # Recursively fetch this sitemap
                                    break

                except:
                    continue

            return TaskResult(
                success=True,
                data={
                    "base_url": base_url,
                    "urls_found": found_urls,
                    "total_urls": len(found_urls),
                    "max_pages": max_pages
                },
                metadata={"base_url": base_url, "urls_count": len(found_urls)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in sitemap crawling: {e}")

    async def _performance_test(self, params: str) -> TaskResult:
        """Test website performance. Format: url|iterations"""
        try:
            parts = params.split("|", 1)
            url = parts[0].strip()
            iterations = int(parts[1].strip()) if len(parts) > 1 and parts[1].strip().isdigit() else 3

            if not url:
                return TaskResult(success=False, error="URL is required")

            performance_data = {
                "url": url,
                "iterations": iterations,
                "results": []
            }

            total_time = 0
            successful_requests = 0

            for i in range(iterations):
                start_time = time.time()

                try:
                    result = await self._fetch_url(url)
                    end_time = time.time()
                    response_time = end_time - start_time

                    if result.success:
                        successful_requests += 1
                        total_time += response_time

                        performance_data["results"].append({
                            "iteration": i + 1,
                            "response_time": round(response_time * 1000, 2),  # ms
                            "status_code": result.data["status_code"],
                            "content_length": len(result.data["content"]),
                            "success": True
                        })
                    else:
                        performance_data["results"].append({
                            "iteration": i + 1,
                            "success": False,
                            "error": result.error
                        })

                except Exception as e:
                    performance_data["results"].append({
                        "iteration": i + 1,
                        "success": False,
                        "error": str(e)
                    })

                # Small delay between requests
                await asyncio.sleep(0.5)

            # Calculate statistics
            if successful_requests > 0:
                avg_response_time = (total_time / successful_requests) * 1000  # ms
                performance_data["statistics"] = {
                    "average_response_time_ms": round(avg_response_time, 2),
                    "success_rate": f"{successful_requests/iterations*100:.1f}%",
                    "successful_requests": successful_requests,
                    "total_requests": iterations
                }

            return TaskResult(
                success=True,
                data=performance_data,
                metadata={"url": url, "iterations": iterations}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in performance test: {e}")

    async def _accessibility_check(self, params: str) -> TaskResult:
        """Basic accessibility check. Format: url"""
        try:
            url = params.strip()

            if not url:
                return TaskResult(success=False, error="URL is required")

            # Fetch the page
            result = await self._fetch_url(url)
            if not result.success:
                return result

            content = result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')

            accessibility_issues = []
            accessibility_score = 100

            # Check for alt text on images
            images = soup.find_all('img')
            images_without_alt = [img for img in images if not img.get('alt')]
            if images_without_alt:
                accessibility_issues.append(f"{len(images_without_alt)} images missing alt text")
                accessibility_score -= min(20, len(images_without_alt) * 2)

            # Check for heading structure
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            h1_count = len(soup.find_all('h1'))
            if h1_count == 0:
                accessibility_issues.append("No H1 heading found")
                accessibility_score -= 15
            elif h1_count > 1:
                accessibility_issues.append("Multiple H1 headings found")
                accessibility_score -= 10

            # Check for form labels
            inputs = soup.find_all('input', type=['text', 'email', 'password', 'tel'])
            inputs_without_labels = []
            for input_elem in inputs:
                input_id = input_elem.get('id')
                if not input_id or not soup.find('label', attrs={'for': input_id}):
                    inputs_without_labels.append(input_elem)

            if inputs_without_labels:
                accessibility_issues.append(f"{len(inputs_without_labels)} form inputs missing labels")
                accessibility_score -= min(15, len(inputs_without_labels) * 3)

            # Check for color contrast (basic check)
            if not soup.find('style') and not soup.find('link', rel='stylesheet'):
                accessibility_issues.append("No CSS found - color contrast cannot be verified")
                accessibility_score -= 5

            accessibility_data = {
                "url": url,
                "score": max(0, accessibility_score),
                "issues": accessibility_issues,
                "checks_performed": [
                    "Image alt text",
                    "Heading structure",
                    "Form labels",
                    "CSS presence"
                ],
                "total_images": len(images),
                "total_headings": len(headings),
                "total_inputs": len(inputs)
            }

            return TaskResult(
                success=True,
                data=accessibility_data,
                metadata={"url": url, "score": accessibility_score}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in accessibility check: {e}")

    async def _seo_analysis(self, params: str) -> TaskResult:
        """Basic SEO analysis. Format: url"""
        try:
            url = params.strip()

            if not url:
                return TaskResult(success=False, error="URL is required")

            # Fetch the page
            result = await self._fetch_url(url)
            if not result.success:
                return result

            content = result.data["content"]
            soup = BeautifulSoup(content, 'html.parser')

            seo_data = {
                "url": url,
                "title": soup.title.string if soup.title else None,
                "meta_description": self._get_meta_content(soup, "description"),
                "meta_keywords": self._get_meta_content(soup, "keywords"),
                "headings": {},
                "images": {
                    "total": len(soup.find_all('img')),
                    "with_alt": len([img for img in soup.find_all('img') if img.get('alt')]),
                    "without_alt": len([img for img in soup.find_all('img') if not img.get('alt')])
                },
                "links": {
                    "internal": 0,
                    "external": 0,
                    "total": 0
                },
                "issues": [],
                "score": 100
            }

            # Analyze headings
            for i in range(1, 7):
                headings = soup.find_all(f'h{i}')
                seo_data["headings"][f"h{i}"] = len(headings)

            # Check title
            if not seo_data["title"]:
                seo_data["issues"].append("Missing title tag")
                seo_data["score"] -= 20
            elif len(seo_data["title"]) > 60:
                seo_data["issues"].append("Title too long (>60 characters)")
                seo_data["score"] -= 10
            elif len(seo_data["title"]) < 30:
                seo_data["issues"].append("Title too short (<30 characters)")
                seo_data["score"] -= 5

            # Check meta description
            if not seo_data["meta_description"]:
                seo_data["issues"].append("Missing meta description")
                seo_data["score"] -= 15
            elif len(seo_data["meta_description"]) > 160:
                seo_data["issues"].append("Meta description too long (>160 characters)")
                seo_data["score"] -= 5

            # Check H1
            if seo_data["headings"]["h1"] == 0:
                seo_data["issues"].append("Missing H1 tag")
                seo_data["score"] -= 15
            elif seo_data["headings"]["h1"] > 1:
                seo_data["issues"].append("Multiple H1 tags")
                seo_data["score"] -= 10

            # Analyze links
            links = soup.find_all('a', href=True)
            seo_data["links"]["total"] = len(links)

            for link in links:
                href = link.get('href', '')
                if href.startswith('http') and url not in href:
                    seo_data["links"]["external"] += 1
                else:
                    seo_data["links"]["internal"] += 1

            # Check images
            if seo_data["images"]["without_alt"] > 0:
                seo_data["issues"].append(f"{seo_data['images']['without_alt']} images missing alt text")
                seo_data["score"] -= min(10, seo_data["images"]["without_alt"] * 2)

            seo_data["score"] = max(0, seo_data["score"])

            return TaskResult(
                success=True,
                data=seo_data,
                metadata={"url": url, "seo_score": seo_data["score"]}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in SEO analysis: {e}")
