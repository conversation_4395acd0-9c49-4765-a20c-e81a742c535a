#!/usr/bin/env python3
"""
Basic usage examples for the Augment Coding Assistant.

This script demonstrates how to use the assistant programmatically
and showcases various capabilities.
"""

import asyncio
import os
from pathlib import Path

from augment_assistant.core.config import Config
from augment_assistant.core.agent import AugmentCodingAssistant


async def basic_file_operations():
    """Demonstrate basic file operations."""
    print("🔧 Basic File Operations Example")
    print("=" * 50)
    
    # Initialize the assistant
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Create a test file
        result = await assistant.process_request(
            "Create a file called 'example.py' with a simple hello world function"
        )
        print(f"✅ File creation: {result.success}")
        if result.success:
            print(f"   Result: {result.data}")
        
        # Read the file back
        result = await assistant.process_request(
            "Read the contents of example.py"
        )
        print(f"✅ File reading: {result.success}")
        if result.success:
            print(f"   Content preview: {str(result.data)[:100]}...")
        
        # Analyze the file
        result = await assistant.process_request(
            "Analyze the code quality of example.py"
        )
        print(f"✅ Code analysis: {result.success}")
        
    finally:
        await assistant.shutdown()


async def web_scraping_example():
    """Demonstrate web scraping capabilities."""
    print("\n🌐 Web Scraping Example")
    print("=" * 50)
    
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Scrape a webpage
        result = await assistant.process_request(
            "Get the title and main headings from https://httpbin.org/html"
        )
        print(f"✅ Web scraping: {result.success}")
        if result.success:
            print(f"   Data extracted successfully")
        
        # Check website status
        result = await assistant.process_request(
            "Check if https://httpbin.org is accessible and get response time"
        )
        print(f"✅ Website status check: {result.success}")
        
    finally:
        await assistant.shutdown()


async def terminal_operations_example():
    """Demonstrate terminal operations."""
    print("\n💻 Terminal Operations Example")
    print("=" * 50)
    
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Execute a simple command
        result = await assistant.process_request(
            "Show the current directory contents"
        )
        print(f"✅ Directory listing: {result.success}")
        
        # Get system information
        result = await assistant.process_request(
            "Show system information including CPU and memory usage"
        )
        print(f"✅ System info: {result.success}")
        
        # Check Python version
        result = await assistant.process_request(
            "Check the Python version installed on this system"
        )
        print(f"✅ Python version check: {result.success}")
        
    finally:
        await assistant.shutdown()


async def code_analysis_example():
    """Demonstrate code analysis capabilities."""
    print("\n🔍 Code Analysis Example")
    print("=" * 50)
    
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Create a sample Python file for analysis
        sample_code = '''
def fibonacci(n):
    """Calculate fibonacci number recursively."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
'''
        
        # Write the sample code to a file
        with open("sample_code.py", "w") as f:
            f.write(sample_code)
        
        # Analyze the code
        result = await assistant.process_request(
            "Analyze the complexity and quality of sample_code.py"
        )
        print(f"✅ Code analysis: {result.success}")
        
        # Find functions in the code
        result = await assistant.process_request(
            "List all functions and classes in sample_code.py with their details"
        )
        print(f"✅ Function/class discovery: {result.success}")
        
        # Check for improvements
        result = await assistant.process_request(
            "Suggest improvements for the code in sample_code.py"
        )
        print(f"✅ Improvement suggestions: {result.success}")
        
        # Clean up
        os.remove("sample_code.py")
        
    finally:
        await assistant.shutdown()


async def advanced_workflow_example():
    """Demonstrate advanced workflow capabilities."""
    print("\n🚀 Advanced Workflow Example")
    print("=" * 50)
    
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Complex multi-step task
        result = await assistant.process_request(
            """
            Create a complete Python project structure for a web scraper:
            1. Create a main directory called 'web_scraper'
            2. Add a main.py file with basic scraping functionality
            3. Create a requirements.txt with necessary dependencies
            4. Add a README.md with usage instructions
            5. Create a config.py for configuration management
            """
        )
        print(f"✅ Project creation: {result.success}")
        
        # Analyze the created project
        result = await assistant.process_request(
            "Analyze the structure and quality of the web_scraper project"
        )
        print(f"✅ Project analysis: {result.success}")
        
    finally:
        await assistant.shutdown()


async def interactive_session_example():
    """Demonstrate how to handle interactive sessions."""
    print("\n🎯 Interactive Session Example")
    print("=" * 50)
    
    config = Config.load_from_env()
    assistant = AugmentCodingAssistant(config)
    
    try:
        # Simulate an interactive conversation
        requests = [
            "What capabilities do you have?",
            "Create a simple Python function to calculate the area of a circle",
            "Now create a test for that function",
            "Run the test and show me the results"
        ]
        
        for i, request in enumerate(requests, 1):
            print(f"\n👤 User: {request}")
            result = await assistant.process_request(request)
            print(f"🤖 Assistant: {'Success' if result.success else 'Failed'}")
            if result.success and isinstance(result.data, str):
                # Show first 200 characters of response
                preview = result.data[:200] + "..." if len(result.data) > 200 else result.data
                print(f"   {preview}")
        
    finally:
        await assistant.shutdown()


async def main():
    """Run all examples."""
    print("🚀 Augment Coding Assistant - Usage Examples")
    print("=" * 60)
    
    # Check if API key is set
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        print("Please set your Gemini API key before running examples:")
        print("export GEMINI_API_KEY='your-api-key-here'")
        return
    
    try:
        await basic_file_operations()
        await web_scraping_example()
        await terminal_operations_example()
        await code_analysis_example()
        await advanced_workflow_example()
        await interactive_session_example()
        
        print("\n🎉 All examples completed successfully!")
        print("Check the created files and explore the assistant's capabilities further.")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have:")
        print("1. Set the GEMINI_API_KEY environment variable")
        print("2. Installed all required dependencies")
        print("3. Have internet connectivity for web operations")


if __name__ == "__main__":
    asyncio.run(main())
