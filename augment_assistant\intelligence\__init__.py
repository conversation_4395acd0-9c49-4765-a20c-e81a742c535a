"""
Project Intelligence Module - Revolutionary architectural understanding.

This module provides project-level intelligence that surpasses all current 2025 coding assistants.
It understands entire codebases, architectural patterns, and provides intelligent insights.

Features:
- Full codebase mapping and understanding
- Architectural pattern recognition
- Cross-file dependency analysis
- Performance bottleneck detection
- Security vulnerability assessment
- Code quality and technical debt analysis
- Refactoring recommendations
- Design pattern suggestions
"""

from .project_analyzer import ProjectAnalyzer
from .architecture_engine import ArchitectureEngine
from .dependency_mapper import DependencyMapper
from .pattern_recognizer import PatternRecognizer
from .quality_assessor import QualityAssessor

__all__ = [
    "ProjectAnalyzer",
    "ArchitectureEngine",
    "DependencyMapper", 
    "PatternRecognizer",
    "QualityAssessor"
]
