[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "augment-coding-assistant"
version = "1.0.0"
description = "A comprehensive Python coding assistant with AI capabilities, file system tools, terminal integration, and web tools"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Augment Code Agent", email = "<EMAIL>"}
]
keywords = ["ai", "coding", "assistant", "terminal", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Code Generators",
    "Topic :: System :: Shells",
    "Topic :: Utilities",
]

dependencies = [
    "google-generativeai>=0.8.0",
    "rich>=13.7.0",
    "textual>=0.45.0",
    "click>=8.1.7",
    "typer>=0.9.0",
    "watchdog>=3.0.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "playwright>=1.40.0",
    "pexpect>=4.9.0",
    "ptyprocess>=0.7.0",
    "psutil>=5.9.0",
    "rope>=1.11.0",
    "jedi>=0.19.0",
    "pydantic>=2.5.0",
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.0",
    "colorama>=0.4.6",
    "tqdm>=4.66.0",
    "humanize>=4.8.0",
    "arrow>=1.3.0",
    "aiofiles>=23.2.1",
    "aiohttp>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
]
advanced = [
    "numpy>=1.24.0",
    "pandas>=2.1.0",
    "matplotlib>=3.8.0",
    "pillow>=10.1.0",
]

[project.scripts]
augment-assistant = "augment_assistant.main:main"
aca = "augment_assistant.main:main"

[project.urls]
Homepage = "https://github.com/augment-code/coding-assistant"
Documentation = "https://github.com/augment-code/coding-assistant/docs"
Repository = "https://github.com/augment-code/coding-assistant"
Issues = "https://github.com/augment-code/coding-assistant/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["augment_assistant*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
