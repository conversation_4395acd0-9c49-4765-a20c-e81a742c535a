"""
Tests for filesystem capabilities.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path

from augment_assistant.capabilities.filesystem import FileSystemAgent
from augment_assistant.core.config import FileSystemConfig
from augment_assistant.core.base import TaskResult


class TestFileSystemAgent:
    """Test filesystem agent capabilities."""
    
    @pytest.fixture
    def fs_agent(self):
        """Create filesystem agent for testing."""
        config = FileSystemConfig()
        return FileSystemAgent(config)
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.mark.asyncio
    async def test_read_file(self, fs_agent, temp_dir):
        """Test reading a file."""
        # Create test file
        test_file = temp_dir / "test.txt"
        test_content = "Hello, World!"
        test_file.write_text(test_content)
        
        # Test reading
        result = await fs_agent.execute("read_file", parameters=str(test_file))
        
        assert result.success is True
        assert result.data == test_content
        assert result.metadata["path"] == str(test_file)
    
    @pytest.mark.asyncio
    async def test_write_file(self, fs_agent, temp_dir):
        """Test writing a file."""
        test_file = temp_dir / "write_test.txt"
        test_content = "Test content for writing"
        
        # Test writing
        result = await fs_agent.execute("write_file", parameters=f"{test_file}|{test_content}")
        
        assert result.success is True
        assert test_file.exists()
        assert test_file.read_text() == test_content
    
    @pytest.mark.asyncio
    async def test_list_directory(self, fs_agent, temp_dir):
        """Test listing directory contents."""
        # Create test files
        (temp_dir / "file1.txt").write_text("content1")
        (temp_dir / "file2.py").write_text("content2")
        (temp_dir / "subdir").mkdir()
        
        # Test listing
        result = await fs_agent.execute("list_directory", parameters=str(temp_dir))
        
        assert result.success is True
        assert len(result.data) == 3
        
        # Check file types
        items = {item["name"]: item for item in result.data}
        assert items["file1.txt"]["type"] == "file"
        assert items["file2.py"]["type"] == "file"
        assert items["subdir"]["type"] == "directory"
    
    @pytest.mark.asyncio
    async def test_create_directory(self, fs_agent, temp_dir):
        """Test creating a directory."""
        new_dir = temp_dir / "new_directory"
        
        # Test creation
        result = await fs_agent.execute("create_directory", parameters=str(new_dir))
        
        assert result.success is True
        assert new_dir.exists()
        assert new_dir.is_dir()
    
    @pytest.mark.asyncio
    async def test_delete_file(self, fs_agent, temp_dir):
        """Test deleting a file."""
        test_file = temp_dir / "to_delete.txt"
        test_file.write_text("content")
        
        # Test deletion
        result = await fs_agent.execute("delete_file", parameters=str(test_file))
        
        assert result.success is True
        assert not test_file.exists()
    
    @pytest.mark.asyncio
    async def test_copy_file(self, fs_agent, temp_dir):
        """Test copying a file."""
        source_file = temp_dir / "source.txt"
        dest_file = temp_dir / "destination.txt"
        test_content = "Content to copy"
        
        source_file.write_text(test_content)
        
        # Test copying
        result = await fs_agent.execute("copy_file", parameters=f"{source_file}|{dest_file}")
        
        assert result.success is True
        assert dest_file.exists()
        assert dest_file.read_text() == test_content
        assert source_file.exists()  # Original should still exist
    
    @pytest.mark.asyncio
    async def test_move_file(self, fs_agent, temp_dir):
        """Test moving a file."""
        source_file = temp_dir / "source.txt"
        dest_file = temp_dir / "moved.txt"
        test_content = "Content to move"
        
        source_file.write_text(test_content)
        
        # Test moving
        result = await fs_agent.execute("move_file", parameters=f"{source_file}|{dest_file}")
        
        assert result.success is True
        assert dest_file.exists()
        assert dest_file.read_text() == test_content
        assert not source_file.exists()  # Original should be gone
    
    @pytest.mark.asyncio
    async def test_search_files(self, fs_agent, temp_dir):
        """Test searching for files."""
        # Create test files
        (temp_dir / "test1.py").write_text("python code")
        (temp_dir / "test2.txt").write_text("text file")
        (temp_dir / "script.py").write_text("more python")
        
        # Test searching for Python files
        result = await fs_agent.execute("search_files", parameters=f"{temp_dir}|*.py")
        
        assert result.success is True
        assert len(result.data) == 2
        
        # Check that we found the right files
        found_names = {item["name"] for item in result.data}
        assert "test1.py" in found_names
        assert "script.py" in found_names
        assert "test2.txt" not in found_names
    
    @pytest.mark.asyncio
    async def test_get_file_info(self, fs_agent, temp_dir):
        """Test getting file information."""
        test_file = temp_dir / "info_test.txt"
        test_content = "Test content for file info"
        test_file.write_text(test_content)
        
        # Test getting file info
        result = await fs_agent.execute("get_file_info", parameters=str(test_file))
        
        assert result.success is True
        assert result.data["name"] == "info_test.txt"
        assert result.data["type"] == "file"
        assert result.data["size"] == len(test_content)
        assert "md5" in result.data
        assert "created" in result.data
        assert "modified" in result.data
    
    @pytest.mark.asyncio
    async def test_find_and_replace(self, fs_agent, temp_dir):
        """Test find and replace in file."""
        test_file = temp_dir / "replace_test.txt"
        original_content = "Hello World! Hello Universe!"
        test_file.write_text(original_content)
        
        # Test find and replace
        result = await fs_agent.execute("find_and_replace", parameters=f"{test_file}|Hello|Hi")
        
        assert result.success is True
        assert result.metadata["changes"] == 2
        
        # Check file content
        new_content = test_file.read_text()
        assert new_content == "Hi World! Hi Universe!"
    
    @pytest.mark.asyncio
    async def test_compare_files(self, fs_agent, temp_dir):
        """Test comparing two files."""
        file1 = temp_dir / "file1.txt"
        file2 = temp_dir / "file2.txt"
        file3 = temp_dir / "file3.txt"
        
        content1 = "Line 1\nLine 2\nLine 3"
        content2 = "Line 1\nLine 2\nLine 3"  # Identical
        content3 = "Line 1\nDifferent Line\nLine 3"  # Different
        
        file1.write_text(content1)
        file2.write_text(content2)
        file3.write_text(content3)
        
        # Test identical files
        result = await fs_agent.execute("compare_files", parameters=f"{file1}|{file2}")
        assert result.success is True
        assert result.data["identical"] is True
        assert result.data["similarity_percent"] == 100.0
        
        # Test different files
        result = await fs_agent.execute("compare_files", parameters=f"{file1}|{file3}")
        assert result.success is True
        assert result.data["identical"] is False
        assert result.data["similarity_percent"] < 100.0
    
    @pytest.mark.asyncio
    async def test_search_in_files(self, fs_agent, temp_dir):
        """Test searching for text within files."""
        # Create test files with content
        file1 = temp_dir / "search1.txt"
        file2 = temp_dir / "search2.py"
        file3 = temp_dir / "search3.txt"
        
        file1.write_text("This is a test file\nwith multiple lines\nand test content")
        file2.write_text("def test_function():\n    return 'test result'\n    # test comment")
        file3.write_text("No matching content here\njust regular text")
        
        # Test searching for "test"
        result = await fs_agent.execute("search_in_files", parameters=f"{temp_dir}|test|*")
        
        assert result.success is True
        assert len(result.data) == 2  # Should find matches in file1 and file2
        
        # Check that we found the right files
        found_files = {match["file"] for match in result.data}
        assert str(file1) in found_files
        assert str(file2) in found_files
        assert str(file3) not in found_files
    
    @pytest.mark.asyncio
    async def test_nonexistent_file(self, fs_agent):
        """Test handling of non-existent file."""
        result = await fs_agent.execute("read_file", parameters="/nonexistent/file.txt")
        
        assert result.success is False
        assert "not found" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_invalid_task(self, fs_agent):
        """Test handling of invalid task."""
        result = await fs_agent.execute("invalid_task", parameters="")
        
        assert result.success is False
        assert "unknown task" in result.error.lower()
    
    def test_available_actions(self, fs_agent):
        """Test getting available actions."""
        actions = fs_agent.get_available_actions()
        
        expected_actions = [
            "read_file", "write_file", "list_directory", "create_directory",
            "delete_file", "copy_file", "move_file", "search_files",
            "watch_directory", "stop_watching", "get_file_info",
            "find_and_replace", "backup_file", "restore_file",
            "compare_files", "search_in_files"
        ]
        
        for action in expected_actions:
            assert action in actions


if __name__ == "__main__":
    pytest.main([__file__])
