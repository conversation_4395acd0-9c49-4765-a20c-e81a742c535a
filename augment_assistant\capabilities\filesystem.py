"""
File system operations capability for the Augment Coding Assistant.
"""

import os
import shutil
import asyncio
import aiofiles
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import fnmatch
import hashlib
import time
from datetime import datetime

from ..core.base import BaseCapability, TaskResult
from ..core.config import FileSystemConfig


class FileWatcher(FileSystemEventHandler):
    """File system event handler for monitoring changes."""
    
    def __init__(self, callback):
        self.callback = callback
        super().__init__()
    
    def on_modified(self, event):
        if not event.is_directory:
            self.callback("modified", event.src_path)
    
    def on_created(self, event):
        if not event.is_directory:
            self.callback("created", event.src_path)
    
    def on_deleted(self, event):
        if not event.is_directory:
            self.callback("deleted", event.src_path)
    
    def on_moved(self, event):
        if not event.is_directory:
            self.callback("moved", event.src_path, event.dest_path)


class FileSystemAgent(BaseCapability):
    """
    Comprehensive file system operations with intelligent features.
    """
    
    def __init__(self, config: FileSystemConfig):
        super().__init__("filesystem", "File system operations and monitoring")
        self.config = config
        self.observers = {}
        self.file_cache = {}
        self.backup_dir = Path.home() / ".augment_assistant" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def execute(self, task: str, **kwargs) -> TaskResult:
        """Execute a file system task."""
        parameters = kwargs.get("parameters", "")
        
        try:
            # Basic operations
            if task == "read_file":
                return await self._read_file(parameters)
            elif task == "write_file":
                return await self._write_file(parameters)
            elif task == "list_directory" or task == "list_dir":
                return await self._list_directory(parameters)
            elif task == "create_directory":
                return await self._create_directory(parameters)
            elif task == "delete_file":
                return await self._delete_file(parameters)
            elif task == "copy_file":
                return await self._copy_file(parameters)
            elif task == "move_file":
                return await self._move_file(parameters)
            elif task == "search_files" or task == "file_search":
                return await self._search_files(parameters)
            elif task == "watch_directory":
                return await self._watch_directory(parameters)
            elif task == "stop_watching":
                return await self._stop_watching(parameters)
            elif task == "get_file_info":
                return await self._get_file_info(parameters)
            elif task == "find_and_replace":
                return await self._find_and_replace(parameters)
            elif task == "backup_file":
                return await self._backup_file(parameters)
            elif task == "restore_file":
                return await self._restore_file(parameters)
            elif task == "compare_files":
                return await self._compare_files(parameters)
            elif task == "search_in_files" or task == "grep_search":
                return await self._search_in_files(parameters)
            # Enhanced operations
            elif task == "create_file":
                return await self._create_file(parameters)
            elif task == "edit_file" or task == "edit_single_or_multi_file":
                return await self._edit_file(parameters)
            elif task == "insert_edit_into_file":
                return await self._insert_edit_into_file(parameters)
            elif task == "list_code_usages":
                return await self._list_code_usages(parameters)
            elif task == "get_changed_files":
                return await self._get_changed_files(parameters)
            elif task == "get_errors":
                return await self._get_errors(parameters)
            elif task == "semantic_search":
                return await self._semantic_search(parameters)
            elif task == "get_project_setup_info":
                return await self._get_project_setup_info(parameters)
            else:
                return TaskResult(success=False, error=f"Unknown task: {task}")
                
        except Exception as e:
            self.log_error(f"Error executing {task}: {e}")
            return TaskResult(success=False, error=str(e))
    
    def get_available_actions(self) -> List[str]:
        """Get list of available file system actions."""
        return [
            # Basic operations
            "read_file", "write_file", "list_directory", "create_directory",
            "delete_file", "copy_file", "move_file", "search_files",
            "watch_directory", "stop_watching", "get_file_info",
            "find_and_replace", "backup_file", "restore_file",
            "compare_files", "search_in_files",
            # Enhanced operations
            "create_file", "edit_file", "edit_single_or_multi_file",
            "insert_edit_into_file", "file_search", "grep_search",
            "list_dir", "list_code_usages", "get_changed_files",
            "get_errors", "semantic_search", "get_project_setup_info"
        ]
    
    async def _read_file(self, path_str: str) -> TaskResult:
        """Read file content."""
        try:
            path = Path(path_str.strip())
            
            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")
            
            if path.stat().st_size > self.config.max_file_size:
                return TaskResult(success=False, error=f"File too large: {path}")
            
            async with aiofiles.open(path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            return TaskResult(
                success=True,
                data=content,
                metadata={
                    "path": str(path),
                    "size": path.stat().st_size,
                    "modified": datetime.fromtimestamp(path.stat().st_mtime).isoformat()
                }
            )
            
        except UnicodeDecodeError:
            # Try reading as binary for non-text files
            try:
                async with aiofiles.open(path, 'rb') as f:
                    content = await f.read()
                return TaskResult(
                    success=True,
                    data=f"Binary file ({len(content)} bytes)",
                    metadata={"path": str(path), "type": "binary"}
                )
            except Exception as e:
                return TaskResult(success=False, error=f"Error reading binary file: {e}")
        
        except Exception as e:
            return TaskResult(success=False, error=f"Error reading file: {e}")
    
    async def _write_file(self, params: str) -> TaskResult:
        """Write content to file. Format: path|content"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: path|content")
            
            path_str, content = parts
            path = Path(path_str.strip())
            
            # Create backup if file exists
            if path.exists() and self.config.backup_enabled:
                await self._create_backup(path)
            
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            return TaskResult(
                success=True,
                data=f"File written: {path}",
                metadata={"path": str(path), "size": len(content)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error writing file: {e}")
    
    async def _list_directory(self, path_str: str) -> TaskResult:
        """List directory contents."""
        try:
            path = Path(path_str.strip()) if path_str.strip() else Path.cwd()
            
            if not path.exists():
                return TaskResult(success=False, error=f"Directory not found: {path}")
            
            if not path.is_dir():
                return TaskResult(success=False, error=f"Not a directory: {path}")
            
            items = []
            for item in path.iterdir():
                if self._should_ignore(item.name):
                    continue
                
                stat = item.stat()
                items.append({
                    "name": item.name,
                    "type": "directory" if item.is_dir() else "file",
                    "size": stat.st_size if item.is_file() else None,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "path": str(item)
                })
            
            # Sort by type (directories first) then by name
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            return TaskResult(
                success=True,
                data=items,
                metadata={"path": str(path), "count": len(items)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error listing directory: {e}")
    
    async def _create_directory(self, path_str: str) -> TaskResult:
        """Create directory."""
        try:
            path = Path(path_str.strip())
            path.mkdir(parents=True, exist_ok=True)
            
            return TaskResult(
                success=True,
                data=f"Directory created: {path}",
                metadata={"path": str(path)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error creating directory: {e}")
    
    async def _delete_file(self, path_str: str) -> TaskResult:
        """Delete file or directory."""
        try:
            path = Path(path_str.strip())
            
            if not path.exists():
                return TaskResult(success=False, error=f"Path not found: {path}")
            
            # Create backup before deletion
            if self.config.backup_enabled:
                await self._create_backup(path)
            
            if path.is_file():
                path.unlink()
                action = "File deleted"
            else:
                shutil.rmtree(path)
                action = "Directory deleted"
            
            return TaskResult(
                success=True,
                data=f"{action}: {path}",
                metadata={"path": str(path)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error deleting: {e}")
    
    async def _search_files(self, params: str) -> TaskResult:
        """Search for files by pattern. Format: directory|pattern"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: directory|pattern")
            
            directory_str, pattern = parts
            directory = Path(directory_str.strip())
            
            if not directory.exists() or not directory.is_dir():
                return TaskResult(success=False, error=f"Invalid directory: {directory}")
            
            matches = []
            for path in directory.rglob(pattern.strip()):
                if self._should_ignore(path.name):
                    continue
                
                matches.append({
                    "path": str(path),
                    "name": path.name,
                    "type": "directory" if path.is_dir() else "file",
                    "size": path.stat().st_size if path.is_file() else None
                })
            
            return TaskResult(
                success=True,
                data=matches,
                metadata={"directory": str(directory), "pattern": pattern, "count": len(matches)}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error searching files: {e}")
    
    def _should_ignore(self, name: str) -> bool:
        """Check if file/directory should be ignored."""
        for pattern in self.config.ignore_patterns:
            if fnmatch.fnmatch(name, pattern):
                return True
        return False
    
    async def _create_backup(self, path: Path) -> None:
        """Create backup of a file or directory."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{path.name}_{timestamp}"
            backup_path = self.backup_dir / backup_name
            
            if path.is_file():
                shutil.copy2(path, backup_path)
            else:
                shutil.copytree(path, backup_path)
                
            self.log_info(f"Backup created: {backup_path}")
            
        except Exception as e:
            self.log_error(f"Error creating backup: {e}")
    
    async def _find_and_replace(self, params: str) -> TaskResult:
        """Find and replace text in file. Format: path|find_text|replace_text"""
        try:
            parts = params.split("|", 2)
            if len(parts) != 3:
                return TaskResult(success=False, error="Invalid format. Use: path|find_text|replace_text")
            
            path_str, find_text, replace_text = parts
            path = Path(path_str.strip())
            
            if not path.exists() or not path.is_file():
                return TaskResult(success=False, error=f"File not found: {path}")
            
            # Create backup
            if self.config.backup_enabled:
                await self._create_backup(path)
            
            # Read, replace, and write
            async with aiofiles.open(path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            original_content = content
            content = content.replace(find_text, replace_text)
            
            async with aiofiles.open(path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            changes = original_content.count(find_text)
            
            return TaskResult(
                success=True,
                data=f"Replaced {changes} occurrences in {path}",
                metadata={"path": str(path), "changes": changes}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error in find and replace: {e}")

    async def _copy_file(self, params: str) -> TaskResult:
        """Copy file or directory. Format: source|destination"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: source|destination")

            source_str, dest_str = parts
            source = Path(source_str.strip())
            dest = Path(dest_str.strip())

            if not source.exists():
                return TaskResult(success=False, error=f"Source not found: {source}")

            if source.is_file():
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, dest)
                action = "File copied"
            else:
                shutil.copytree(source, dest, dirs_exist_ok=True)
                action = "Directory copied"

            return TaskResult(
                success=True,
                data=f"{action}: {source} -> {dest}",
                metadata={"source": str(source), "destination": str(dest)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error copying: {e}")

    async def _move_file(self, params: str) -> TaskResult:
        """Move file or directory. Format: source|destination"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: source|destination")

            source_str, dest_str = parts
            source = Path(source_str.strip())
            dest = Path(dest_str.strip())

            if not source.exists():
                return TaskResult(success=False, error=f"Source not found: {source}")

            dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(dest))

            return TaskResult(
                success=True,
                data=f"Moved: {source} -> {dest}",
                metadata={"source": str(source), "destination": str(dest)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error moving: {e}")

    async def _get_file_info(self, path_str: str) -> TaskResult:
        """Get detailed file information."""
        try:
            path = Path(path_str.strip())

            if not path.exists():
                return TaskResult(success=False, error=f"Path not found: {path}")

            stat = path.stat()
            info = {
                "path": str(path),
                "name": path.name,
                "type": "directory" if path.is_dir() else "file",
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "permissions": oct(stat.st_mode)[-3:],
                "owner": stat.st_uid,
                "group": stat.st_gid
            }

            if path.is_file():
                # Calculate file hash
                hash_md5 = hashlib.md5()
                with open(path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
                info["md5"] = hash_md5.hexdigest()

                # Get file extension and MIME type
                info["extension"] = path.suffix
                info["stem"] = path.stem

            return TaskResult(success=True, data=info)

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting file info: {e}")

    async def _watch_directory(self, path_str: str) -> TaskResult:
        """Start watching a directory for changes."""
        try:
            path = Path(path_str.strip())

            if not path.exists() or not path.is_dir():
                return TaskResult(success=False, error=f"Invalid directory: {path}")

            if str(path) in self.observers:
                return TaskResult(success=False, error=f"Already watching: {path}")

            def file_change_callback(event_type, file_path, dest_path=None):
                self.log_info(f"File {event_type}: {file_path}")
                if dest_path:
                    self.log_info(f"  -> {dest_path}")

            event_handler = FileWatcher(file_change_callback)
            observer = Observer()
            observer.schedule(event_handler, str(path), recursive=True)
            observer.start()

            self.observers[str(path)] = observer

            return TaskResult(
                success=True,
                data=f"Started watching: {path}",
                metadata={"path": str(path)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error starting file watcher: {e}")

    async def _stop_watching(self, path_str: str) -> TaskResult:
        """Stop watching a directory."""
        try:
            path_str = path_str.strip()

            if path_str not in self.observers:
                return TaskResult(success=False, error=f"Not watching: {path_str}")

            observer = self.observers[path_str]
            observer.stop()
            observer.join()
            del self.observers[path_str]

            return TaskResult(
                success=True,
                data=f"Stopped watching: {path_str}",
                metadata={"path": path_str}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error stopping file watcher: {e}")

    async def _backup_file(self, path_str: str) -> TaskResult:
        """Create backup of a file."""
        try:
            path = Path(path_str.strip())

            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")

            await self._create_backup(path)

            return TaskResult(
                success=True,
                data=f"Backup created for: {path}",
                metadata={"path": str(path)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error creating backup: {e}")

    async def _restore_file(self, params: str) -> TaskResult:
        """Restore file from backup. Format: original_path|backup_timestamp"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: original_path|backup_timestamp")

            original_path_str, timestamp = parts
            original_path = Path(original_path_str.strip())

            backup_name = f"{original_path.name}_{timestamp.strip()}"
            backup_path = self.backup_dir / backup_name

            if not backup_path.exists():
                return TaskResult(success=False, error=f"Backup not found: {backup_path}")

            if backup_path.is_file():
                shutil.copy2(backup_path, original_path)
            else:
                if original_path.exists():
                    shutil.rmtree(original_path)
                shutil.copytree(backup_path, original_path)

            return TaskResult(
                success=True,
                data=f"Restored: {original_path} from {backup_path}",
                metadata={"original": str(original_path), "backup": str(backup_path)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error restoring file: {e}")

    async def _compare_files(self, params: str) -> TaskResult:
        """Compare two files. Format: file1|file2"""
        try:
            parts = params.split("|", 1)
            if len(parts) != 2:
                return TaskResult(success=False, error="Invalid format. Use: file1|file2")

            file1_str, file2_str = parts
            file1 = Path(file1_str.strip())
            file2 = Path(file2_str.strip())

            if not file1.exists() or not file2.exists():
                return TaskResult(success=False, error="One or both files not found")

            # Compare file sizes
            size1 = file1.stat().st_size
            size2 = file2.stat().st_size

            # Compare content
            async with aiofiles.open(file1, 'r', encoding='utf-8') as f1:
                content1 = await f1.read()

            async with aiofiles.open(file2, 'r', encoding='utf-8') as f2:
                content2 = await f2.read()

            identical = content1 == content2

            # Calculate similarity if not identical
            similarity = 100.0 if identical else 0.0
            if not identical:
                # Simple similarity calculation
                lines1 = content1.splitlines()
                lines2 = content2.splitlines()
                common_lines = len(set(lines1) & set(lines2))
                total_lines = len(set(lines1) | set(lines2))
                similarity = (common_lines / total_lines * 100) if total_lines > 0 else 0.0

            result = {
                "identical": identical,
                "similarity_percent": round(similarity, 2),
                "file1": {"path": str(file1), "size": size1, "lines": len(content1.splitlines())},
                "file2": {"path": str(file2), "size": size2, "lines": len(content2.splitlines())}
            }

            return TaskResult(success=True, data=result)

        except Exception as e:
            return TaskResult(success=False, error=f"Error comparing files: {e}")

    async def _search_in_files(self, params: str) -> TaskResult:
        """Search for text within files. Format: directory|search_text|file_pattern"""
        try:
            parts = params.split("|", 2)
            if len(parts) < 2:
                return TaskResult(success=False, error="Invalid format. Use: directory|search_text|file_pattern")

            directory_str = parts[0].strip()
            search_text = parts[1].strip()
            file_pattern = parts[2].strip() if len(parts) > 2 else "*"

            directory = Path(directory_str)

            if not directory.exists() or not directory.is_dir():
                return TaskResult(success=False, error=f"Invalid directory: {directory}")

            matches = []

            for file_path in directory.rglob(file_pattern):
                if not file_path.is_file() or self._should_ignore(file_path.name):
                    continue

                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        content = await f.read()

                    lines = content.splitlines()
                    file_matches = []

                    for line_num, line in enumerate(lines, 1):
                        if search_text.lower() in line.lower():
                            file_matches.append({
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "context": {
                                    "before": lines[max(0, line_num-2):line_num-1],
                                    "after": lines[line_num:min(len(lines), line_num+2)]
                                }
                            })

                    if file_matches:
                        matches.append({
                            "file": str(file_path),
                            "matches": file_matches,
                            "total_matches": len(file_matches)
                        })

                except (UnicodeDecodeError, PermissionError):
                    # Skip binary files or files we can't read
                    continue

            return TaskResult(
                success=True,
                data=matches,
                metadata={
                    "directory": str(directory),
                    "search_text": search_text,
                    "file_pattern": file_pattern,
                    "files_with_matches": len(matches),
                    "total_matches": sum(m["total_matches"] for m in matches)
                }
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error searching in files: {e}")

    async def shutdown(self) -> None:
        """Shutdown the file system agent."""
        # Stop all file watchers
        for path, observer in self.observers.items():
            try:
                observer.stop()
                observer.join()
                self.log_info(f"Stopped watching: {path}")
            except Exception as e:
                self.log_error(f"Error stopping watcher for {path}: {e}")

        self.observers.clear()
        self.log_info("File system agent shutdown complete")

    # Enhanced file system operations
    async def _create_file(self, params: str) -> TaskResult:
        """Create a file with content. Format: path|content"""
        return await self._write_file(params)  # Same as write_file

    async def _edit_file(self, params: str) -> TaskResult:
        """Edit file by inserting, deleting, or modifying lines. Format: path|operation|line_number|content"""
        try:
            parts = params.split("|", 3)
            if len(parts) < 3:
                return TaskResult(success=False, error="Invalid format. Use: path|operation|line_number|content")

            file_path = parts[0].strip()
            operation = parts[1].strip().lower()
            line_number = int(parts[2].strip())
            content = parts[3].strip() if len(parts) > 3 else ""

            path = Path(file_path)
            if not path.exists():
                return TaskResult(success=False, error=f"File not found: {path}")

            # Create backup
            if self.config.backup_enabled:
                await self._create_backup(path)

            # Read current content
            async with aiofiles.open(path, 'r', encoding='utf-8') as f:
                lines = (await f.read()).splitlines()

            # Perform operation
            if operation == "insert":
                lines.insert(line_number - 1, content)
            elif operation == "delete":
                if 1 <= line_number <= len(lines):
                    del lines[line_number - 1]
            elif operation == "replace":
                if 1 <= line_number <= len(lines):
                    lines[line_number - 1] = content
            elif operation == "append":
                lines.append(content)
            else:
                return TaskResult(success=False, error=f"Unknown operation: {operation}")

            # Write back
            async with aiofiles.open(path, 'w', encoding='utf-8') as f:
                await f.write('\n'.join(lines))

            return TaskResult(
                success=True,
                data=f"File edited: {operation} at line {line_number}",
                metadata={"path": str(path), "operation": operation, "line": line_number}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error editing file: {e}")

    async def _insert_edit_into_file(self, params: str) -> TaskResult:
        """Insert content at specific location. Format: path|line_number|content"""
        try:
            parts = params.split("|", 2)
            if len(parts) != 3:
                return TaskResult(success=False, error="Invalid format. Use: path|line_number|content")

            file_path, line_str, content = parts
            return await self._edit_file(f"{file_path}|insert|{line_str}|{content}")

        except Exception as e:
            return TaskResult(success=False, error=f"Error inserting into file: {e}")

    async def _list_code_usages(self, params: str) -> TaskResult:
        """Find where symbols (functions/classes) are used. Format: symbol_name|directory"""
        try:
            parts = params.split("|", 1)
            if len(parts) < 1:
                return TaskResult(success=False, error="Symbol name is required")

            symbol_name = parts[0].strip()
            directory = Path(parts[1].strip()) if len(parts) > 1 else Path.cwd()

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            usages = []

            # Search in Python files
            for py_file in directory.rglob("*.py"):
                if self._should_ignore(py_file.name):
                    continue

                try:
                    async with aiofiles.open(py_file, 'r', encoding='utf-8') as f:
                        content = await f.read()

                    lines = content.splitlines()
                    for line_num, line in enumerate(lines, 1):
                        if symbol_name in line:
                            # Check if it's a real usage (not in comments or strings)
                            stripped = line.strip()
                            if not stripped.startswith('#') and symbol_name in stripped:
                                usages.append({
                                    "file": str(py_file),
                                    "line": line_num,
                                    "content": line.strip(),
                                    "type": self._detect_usage_type(line, symbol_name)
                                })

                except (UnicodeDecodeError, PermissionError):
                    continue

            return TaskResult(
                success=True,
                data=usages,
                metadata={"symbol": symbol_name, "directory": str(directory), "usages_found": len(usages)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error finding code usages: {e}")

    def _detect_usage_type(self, line: str, symbol: str) -> str:
        """Detect the type of symbol usage."""
        line = line.strip()
        if f"def {symbol}" in line:
            return "definition"
        elif f"class {symbol}" in line:
            return "class_definition"
        elif f"import {symbol}" in line or f"from {symbol}" in line:
            return "import"
        elif f"{symbol}(" in line:
            return "function_call"
        elif f"{symbol}." in line:
            return "method_call"
        else:
            return "reference"

    async def _get_changed_files(self, params: str) -> TaskResult:
        """Show Git diffs of modified files. Format: directory"""
        try:
            directory = Path(params.strip()) if params.strip() else Path.cwd()

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            # Check if it's a git repository
            git_dir = directory / ".git"
            if not git_dir.exists():
                return TaskResult(success=False, error="Not a git repository")

            import subprocess

            # Get changed files
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=directory,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                return TaskResult(success=False, error=f"Git error: {result.stderr}")

            changed_files = []
            for line in result.stdout.splitlines():
                if line.strip():
                    status = line[:2]
                    filename = line[3:]
                    changed_files.append({
                        "file": filename,
                        "status": status.strip(),
                        "status_description": self._get_git_status_description(status)
                    })

            # Get diff for each file
            for file_info in changed_files:
                if file_info["status"] in ["M", "MM"]:  # Modified files
                    diff_result = subprocess.run(
                        ["git", "diff", file_info["file"]],
                        cwd=directory,
                        capture_output=True,
                        text=True
                    )
                    if diff_result.returncode == 0:
                        file_info["diff"] = diff_result.stdout

            return TaskResult(
                success=True,
                data=changed_files,
                metadata={"directory": str(directory), "changed_files_count": len(changed_files)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting changed files: {e}")

    def _get_git_status_description(self, status: str) -> str:
        """Get description for git status code."""
        status_map = {
            "M": "Modified",
            "A": "Added",
            "D": "Deleted",
            "R": "Renamed",
            "C": "Copied",
            "U": "Unmerged",
            "?": "Untracked",
            "!": "Ignored"
        }
        return status_map.get(status.strip(), "Unknown")

    async def _get_errors(self, params: str) -> TaskResult:
        """Fetch lint, syntax, or compiler errors. Format: directory|file_pattern"""
        try:
            parts = params.split("|", 1)
            directory = Path(parts[0].strip()) if parts[0].strip() else Path.cwd()
            file_pattern = parts[1].strip() if len(parts) > 1 else "*.py"

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            errors = []

            # Check Python files for syntax errors
            for file_path in directory.rglob(file_pattern):
                if self._should_ignore(file_path.name):
                    continue

                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        content = await f.read()

                    # Check syntax
                    try:
                        compile(content, str(file_path), 'exec')
                    except SyntaxError as e:
                        errors.append({
                            "file": str(file_path),
                            "type": "syntax_error",
                            "line": e.lineno,
                            "column": e.offset,
                            "message": str(e),
                            "text": e.text
                        })

                except (UnicodeDecodeError, PermissionError):
                    continue

            return TaskResult(
                success=True,
                data=errors,
                metadata={"directory": str(directory), "pattern": file_pattern, "errors_found": len(errors)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting errors: {e}")

    async def _semantic_search(self, params: str) -> TaskResult:
        """Natural language search across codebase. Format: query|directory"""
        try:
            parts = params.split("|", 1)
            if len(parts) < 1:
                return TaskResult(success=False, error="Search query is required")

            query = parts[0].strip()
            directory = Path(parts[1].strip()) if len(parts) > 1 else Path.cwd()

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            # Simple semantic search based on keywords and context
            results = []
            query_words = query.lower().split()

            for file_path in directory.rglob("*.py"):
                if self._should_ignore(file_path.name):
                    continue

                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        content = await f.read()

                    lines = content.splitlines()
                    file_score = 0
                    matching_lines = []

                    for line_num, line in enumerate(lines, 1):
                        line_lower = line.lower()
                        line_score = 0

                        # Score based on query words
                        for word in query_words:
                            if word in line_lower:
                                line_score += 1
                                # Bonus for exact matches
                                if word in line_lower.split():
                                    line_score += 1

                        if line_score > 0:
                            file_score += line_score
                            matching_lines.append({
                                "line": line_num,
                                "content": line.strip(),
                                "score": line_score
                            })

                    if file_score > 0:
                        results.append({
                            "file": str(file_path),
                            "score": file_score,
                            "matching_lines": sorted(matching_lines, key=lambda x: x["score"], reverse=True)[:10]
                        })

                except (UnicodeDecodeError, PermissionError):
                    continue

            # Sort by relevance score
            results.sort(key=lambda x: x["score"], reverse=True)

            return TaskResult(
                success=True,
                data=results[:20],  # Top 20 results
                metadata={"query": query, "directory": str(directory), "results_found": len(results)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error in semantic search: {e}")

    async def _get_project_setup_info(self, params: str) -> TaskResult:
        """Detect framework, language, tooling, etc. Format: directory"""
        try:
            directory = Path(params.strip()) if params.strip() else Path.cwd()

            if not directory.exists():
                return TaskResult(success=False, error=f"Directory not found: {directory}")

            project_info = {
                "directory": str(directory),
                "languages": [],
                "frameworks": [],
                "tools": [],
                "package_managers": [],
                "config_files": [],
                "structure": {}
            }

            # Detect languages by file extensions
            language_map = {
                ".py": "Python",
                ".js": "JavaScript",
                ".ts": "TypeScript",
                ".java": "Java",
                ".cpp": "C++",
                ".c": "C",
                ".cs": "C#",
                ".go": "Go",
                ".rs": "Rust",
                ".php": "PHP",
                ".rb": "Ruby",
                ".swift": "Swift",
                ".kt": "Kotlin"
            }

            file_counts = {}
            for file_path in directory.rglob("*"):
                if file_path.is_file() and not self._should_ignore(file_path.name):
                    ext = file_path.suffix.lower()
                    if ext in language_map:
                        lang = language_map[ext]
                        file_counts[lang] = file_counts.get(lang, 0) + 1

            project_info["languages"] = [{"language": lang, "files": count} for lang, count in file_counts.items()]

            # Detect frameworks and tools by config files
            config_files = {
                "package.json": {"type": "Node.js", "framework": "JavaScript/TypeScript"},
                "requirements.txt": {"type": "Python", "tool": "pip"},
                "pyproject.toml": {"type": "Python", "tool": "modern Python packaging"},
                "Pipfile": {"type": "Python", "tool": "pipenv"},
                "poetry.lock": {"type": "Python", "tool": "poetry"},
                "setup.py": {"type": "Python", "tool": "setuptools"},
                "Cargo.toml": {"type": "Rust", "tool": "cargo"},
                "go.mod": {"type": "Go", "tool": "go modules"},
                "pom.xml": {"type": "Java", "tool": "Maven"},
                "build.gradle": {"type": "Java", "tool": "Gradle"},
                "composer.json": {"type": "PHP", "tool": "Composer"},
                "Gemfile": {"type": "Ruby", "tool": "Bundler"},
                ".gitignore": {"type": "Git", "tool": "version control"},
                "Dockerfile": {"type": "Docker", "tool": "containerization"},
                "docker-compose.yml": {"type": "Docker", "tool": "container orchestration"},
                "tsconfig.json": {"type": "TypeScript", "tool": "TypeScript compiler"},
                "webpack.config.js": {"type": "JavaScript", "tool": "Webpack"},
                "vite.config.js": {"type": "JavaScript", "tool": "Vite"},
                "next.config.js": {"type": "JavaScript", "framework": "Next.js"},
                "nuxt.config.js": {"type": "JavaScript", "framework": "Nuxt.js"},
                "angular.json": {"type": "JavaScript", "framework": "Angular"},
                "vue.config.js": {"type": "JavaScript", "framework": "Vue.js"},
                "svelte.config.js": {"type": "JavaScript", "framework": "Svelte"},
                "django": {"type": "Python", "framework": "Django"},
                "flask": {"type": "Python", "framework": "Flask"},
                "fastapi": {"type": "Python", "framework": "FastAPI"}
            }

            for config_file, info in config_files.items():
                config_path = directory / config_file
                if config_path.exists():
                    project_info["config_files"].append({
                        "file": config_file,
                        "type": info.get("type"),
                        "tool": info.get("tool"),
                        "framework": info.get("framework")
                    })

                    if info.get("tool"):
                        project_info["tools"].append(info["tool"])
                    if info.get("framework"):
                        project_info["frameworks"].append(info["framework"])

            # Detect Python frameworks by imports
            if any(lang["language"] == "Python" for lang in project_info["languages"]):
                framework_imports = {
                    "django": "Django",
                    "flask": "Flask",
                    "fastapi": "FastAPI",
                    "tornado": "Tornado",
                    "pyramid": "Pyramid",
                    "bottle": "Bottle",
                    "cherrypy": "CherryPy"
                }

                for py_file in directory.rglob("*.py"):
                    try:
                        async with aiofiles.open(py_file, 'r', encoding='utf-8') as f:
                            content = await f.read()

                        for import_name, framework in framework_imports.items():
                            if f"import {import_name}" in content or f"from {import_name}" in content:
                                if framework not in project_info["frameworks"]:
                                    project_info["frameworks"].append(framework)

                    except (UnicodeDecodeError, PermissionError):
                        continue

            # Analyze project structure
            structure = {}
            for item in directory.iterdir():
                if not self._should_ignore(item.name):
                    if item.is_dir():
                        structure[item.name] = "directory"
                    else:
                        structure[item.name] = "file"

            project_info["structure"] = structure

            return TaskResult(
                success=True,
                data=project_info,
                metadata={"directory": str(directory)}
            )

        except Exception as e:
            return TaskResult(success=False, error=f"Error getting project setup info: {e}")
