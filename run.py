#!/usr/bin/env python3
"""
Simple runner script for the Augment Coding Assistant.
This allows running the assistant directly without installation.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import and run the main function
try:
    from augment_assistant.main import main
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you have installed the required dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Error running the assistant: {e}")
    sys.exit(1)
