"""
Reviewer Agent - Specialized in code review and quality assessment.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class ReviewerAgent(BaseAgent):
    """Specialized agent for code review."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("ReviewerAgent", [])
        self.gemini_client = gemini_client
    
    async def review_code(self, code: str) -> TaskResult:
        """Perform comprehensive code review."""
        prompt = f"""
        As a senior code reviewer, review this code:
        
        Code: {code}
        
        Provide:
        1. Code quality assessment
        2. Best practices compliance
        3. Maintainability analysis
        4. Readability improvements
        5. Refactoring suggestions
        """
        
        return await self.gemini_client.generate_response(prompt)
