"""
Architect Agent - Specialized in system design and architecture.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class ArchitectAgent(BaseAgent):
    """Specialized agent for system architecture and design."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("ArchitectAgent", [])
        self.gemini_client = gemini_client
    
    async def design_architecture(self, requirements: str) -> TaskResult:
        """Design system architecture based on requirements."""
        prompt = f"""
        As a senior system architect, design a comprehensive architecture for:
        
        Requirements: {requirements}
        
        Provide:
        1. High-level architecture overview
        2. Component breakdown
        3. Technology recommendations
        4. Scalability considerations
        5. Security architecture
        6. Performance considerations
        """
        
        return await self.gemini_client.generate_response(prompt)
