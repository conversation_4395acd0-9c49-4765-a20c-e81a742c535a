"""
Main entry point for the Augment Coding Assistant.
"""

import asyncio
import sys
import argparse
import logging
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# Handle both direct execution and package import
try:
    from .core.config import Config
    from .core.agent import AugmentCodingAssistant
except ImportError:
    # Add the parent directory to sys.path for direct execution
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from augment_assistant.core.config import Config
    from augment_assistant.core.agent import AugmentCodingAssistant


def setup_logging(config: Config) -> None:
    """Setup logging configuration."""
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    
    # Create logs directory
    logs_dir = config.get_logs_dir()
    log_file = logs_dir / "augment_assistant.log"
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler() if config.debug else logging.NullHandler()
        ]
    )


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--debug', '-d', is_flag=True, help='Enable debug mode')
@click.option('--log-level', '-l', default='INFO', help='Logging level')
@click.pass_context
def cli(ctx, config, debug, log_level):
    """Augment Coding Assistant - Your AI-powered development companion."""
    ctx.ensure_object(dict)
    
    # Load configuration
    if config:
        ctx.obj['config'] = Config.load_from_file(Path(config))
    else:
        ctx.obj['config'] = Config.load_from_env()
    
    # Override with command line options
    if debug:
        ctx.obj['config'].debug = True
    if log_level:
        ctx.obj['config'].log_level = log_level
    
    # Setup logging
    setup_logging(ctx.obj['config'])


@cli.command()
@click.pass_context
async def interactive(ctx):
    """Start interactive terminal session."""
    config = ctx.obj['config']
    
    console = Console()
    console.print(Panel(
        Text.assemble(
            ("🚀 ", "bold blue"),
            ("Starting Augment Coding Assistant", "bold white"),
            (" 🚀", "bold blue")
        ),
        border_style="blue"
    ))
    
    try:
        # Initialize the assistant
        assistant = AugmentCodingAssistant(config)
        
        # Start interactive session
        await assistant.start_interactive_session()
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")
        if config.debug:
            import traceback
            console.print(f"[red]{traceback.format_exc()}[/red]")
    finally:
        if 'assistant' in locals():
            await assistant.shutdown()


@cli.command()
@click.argument('request', required=True)
@click.option('--output', '-o', type=click.Path(), help='Output file for result')
@click.option('--format', '-f', type=click.Choice(['text', 'json', 'yaml']), default='text', help='Output format')
@click.pass_context
async def execute(ctx, request, output, format):
    """Execute a single request and exit."""
    config = ctx.obj['config']
    console = Console()
    
    try:
        # Initialize the assistant
        assistant = AugmentCodingAssistant(config)
        
        # Process the request
        result = await assistant.process_request(request)
        
        # Format output
        if format == 'json':
            import json
            output_text = json.dumps(result.model_dump(), indent=2, default=str)
        elif format == 'yaml':
            import yaml
            output_text = yaml.dump(result.model_dump(), default_flow_style=False)
        else:  # text
            if result.success:
                output_text = str(result.data)
            else:
                output_text = f"Error: {result.error}"
        
        # Output result
        if output:
            with open(output, 'w') as f:
                f.write(output_text)
            console.print(f"[green]Result saved to: {output}[/green]")
        else:
            console.print(output_text)
        
        # Exit with appropriate code
        sys.exit(0 if result.success else 1)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)
    finally:
        if 'assistant' in locals():
            await assistant.shutdown()


@cli.command()
@click.pass_context
def config_show(ctx):
    """Show current configuration."""
    config = ctx.obj['config']
    console = Console()
    
    import json
    from rich.syntax import Syntax
    
    config_json = json.dumps(config.model_dump(), indent=2, default=str)
    syntax = Syntax(config_json, "json", theme="monokai", line_numbers=True)
    
    console.print(Panel(syntax, title="[bold blue]Configuration[/bold blue]"))


@cli.command()
@click.option('--output', '-o', type=click.Path(), default='config.yaml', help='Output configuration file')
@click.pass_context
def config_generate(ctx, output):
    """Generate a configuration file template."""
    config = Config()
    config.save_to_file(Path(output))
    
    console = Console()
    console.print(f"[green]Configuration template saved to: {output}[/green]")


@cli.command()
def version():
    """Show version information."""
    from . import __version__, __author__
    
    console = Console()
    console.print(Panel(
        Text.assemble(
            ("Augment Coding Assistant", "bold blue"),
            f"\nVersion: {__version__}",
            f"\nAuthor: {__author__}",
            "\n\nA comprehensive Python coding assistant with AI capabilities."
        ),
        title="[bold blue]Version Info[/bold blue]",
        border_style="blue"
    ))


@cli.command()
@click.argument('capability', required=True)
@click.pass_context
def capabilities(ctx, capability):
    """Show information about a specific capability."""
    config = ctx.obj['config']
    console = Console()
    
    try:
        assistant = AugmentCodingAssistant(config)
        
        if capability == 'list':
            # List all capabilities
            caps = assistant.list_capabilities()
            console.print("[bold blue]Available Capabilities:[/bold blue]")
            for cap_name in caps:
                cap = assistant.get_capability(cap_name)
                console.print(f"  [cyan]•[/cyan] [bold]{cap_name}[/bold]: {cap.description}")
        else:
            # Show specific capability
            cap = assistant.get_capability(capability)
            if cap:
                console.print(f"[bold blue]Capability: {capability}[/bold blue]")
                console.print(f"Description: {cap.description}")
                console.print(f"Actions: {', '.join(cap.get_available_actions())}")
            else:
                console.print(f"[red]Capability '{capability}' not found[/red]")
                
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")


def main():
    """Main entry point."""
    # Handle async commands
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))
        return wrapper
    
    # Wrap async commands
    cli.commands['interactive'].callback = async_command(cli.commands['interactive'].callback)
    cli.commands['execute'].callback = async_command(cli.commands['execute'].callback)
    
    # Run CLI
    try:
        cli()
    except KeyboardInterrupt:
        console = Console()
        console.print("\n[yellow]Interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console = Console()
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
