"""
Main agent controller for the Augment Coding Assistant.
"""

import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from .base import BaseAgent, BaseCapability, TaskResult, ReasoningChain, Memory
from .config import Config
from ..ai.gemini_client import Gemini<PERSON><PERSON>
from ..ai.reasoning import ReasoningEngine
from ..capabilities.filesystem import FileSystemAgent
from ..capabilities.terminal import TerminalAgent
from ..capabilities.web import WebAgent
from ..capabilities.code_analysis import CodeAnalysisAgent
from ..ui.terminal_interface import TerminalInterface

from rich.console import Console
from rich.panel import Panel
from rich.text import Text


class AugmentCodingAssistant(BaseAgent):
    """
    Main coding assistant that orchestrates all capabilities and provides
    intelligent task routing and execution.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.console = Console()
        self.memory = Memory()
        
        # Initialize AI components
        self.gemini_client = GeminiClient(config.ai)
        self.reasoning_engine = ReasoningEngine(self.gemini_client, config)
        
        # Initialize capabilities
        capabilities = self._initialize_capabilities()
        
        super().__init__("AugmentCodingAssistant", capabilities)
        
        # Initialize UI
        self.ui = TerminalInterface(config.terminal, self)
        
        self.log_info("Augment Coding Assistant initialized successfully")
    
    def _initialize_capabilities(self) -> List[BaseCapability]:
        """Initialize all capabilities."""
        capabilities = []
        
        try:
            # File system capabilities
            fs_agent = FileSystemAgent(self.config.filesystem)
            capabilities.append(fs_agent)
            
            # Terminal capabilities
            terminal_agent = TerminalAgent(self.config)
            capabilities.append(terminal_agent)
            
            # Web capabilities
            web_agent = WebAgent(self.config.web)
            capabilities.append(web_agent)
            
            # Code analysis capabilities
            code_agent = CodeAnalysisAgent(self.config)
            capabilities.append(code_agent)
            
            self.log_info(f"Initialized {len(capabilities)} capabilities")
            
        except Exception as e:
            self.log_error(f"Error initializing capabilities: {e}")
            
        return capabilities
    
    async def process_request(self, request: str, **kwargs) -> TaskResult:
        """
        Process a user request using AI reasoning and appropriate capabilities.
        """
        try:
            self.log_info(f"Processing request: {request[:100]}...")
            
            # Add to conversation memory
            self.memory.add_to_conversation("user", request)
            
            # Use reasoning engine to plan and execute
            reasoning_chain = await self.reasoning_engine.reason_and_act(
                request, 
                available_capabilities=self.list_capabilities(),
                context=self.context,
                memory=self.memory
            )
            
            # Execute the planned actions
            result = await self._execute_reasoning_chain(reasoning_chain)
            
            # Add result to memory
            self.memory.add_to_conversation("assistant", result.data or "Task completed")
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing request: {e}"
            self.log_error(error_msg)
            return TaskResult(success=False, error=error_msg)
    
    async def _execute_reasoning_chain(self, reasoning_chain: ReasoningChain) -> TaskResult:
        """Execute a reasoning chain by performing the planned actions."""
        try:
            results = []
            
            for step in reasoning_chain.steps:
                if step.action:
                    # Parse action and execute
                    action_result = await self._execute_action(step.action)
                    results.append(action_result)
                    
                    # Update step with observation
                    step.observation = str(action_result.data) if action_result.success else action_result.error
            
            # Combine results
            success = all(r.success for r in results)
            combined_data = [r.data for r in results if r.success]
            
            return TaskResult(
                success=success,
                data=reasoning_chain.final_answer or combined_data,
                metadata={"reasoning_chain": reasoning_chain.model_dump()}
            )
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error executing reasoning chain: {e}")
    
    async def _execute_action(self, action: str) -> TaskResult:
        """Execute a specific action using the appropriate capability."""
        try:
            # Parse action format: "capability:action:parameters"
            parts = action.split(":", 2)
            if len(parts) < 2:
                return TaskResult(success=False, error=f"Invalid action format: {action}")
            
            capability_name = parts[0]
            action_name = parts[1]
            parameters = parts[2] if len(parts) > 2 else ""
            
            # Get capability
            capability = self.get_capability(capability_name)
            if not capability:
                return TaskResult(success=False, error=f"Capability not found: {capability_name}")
            
            # Execute action
            return await capability.execute(action_name, parameters=parameters)
            
        except Exception as e:
            return TaskResult(success=False, error=f"Error executing action: {e}")
    
    async def start_interactive_session(self) -> None:
        """Start an interactive terminal session."""
        self.console.print(Panel(
            Text("🚀 Augment Coding Assistant", style="bold blue"),
            subtitle="Your AI-powered development companion",
            border_style="blue"
        ))
        
        self.console.print("\n[green]Available capabilities:[/green]")
        for cap_name in self.list_capabilities():
            capability = self.get_capability(cap_name)
            self.console.print(f"  • [cyan]{cap_name}[/cyan]: {capability.description}")
        
        self.console.print("\n[yellow]Type 'help' for commands, 'quit' to exit[/yellow]\n")
        
        # Start UI
        await self.ui.start_interactive_session()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the assistant."""
        return {
            "capabilities": len(self.capabilities),
            "memory_entries": len(self.memory.conversation_history),
            "config": {
                "ai_model": self.config.ai.model,
                "debug_mode": self.config.debug,
                "working_directory": str(self.config.working_directory)
            }
        }
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the assistant."""
        self.log_info("Shutting down Augment Coding Assistant...")
        
        # Shutdown capabilities
        for capability in self.capabilities.values():
            if hasattr(capability, 'shutdown'):
                try:
                    await capability.shutdown()
                except Exception as e:
                    self.log_error(f"Error shutting down {capability.name}: {e}")
        
        # Shutdown AI client
        if hasattr(self.gemini_client, 'shutdown'):
            await self.gemini_client.shutdown()
        
        self.log_info("Shutdown complete")
    
    def save_session(self, session_path: Path) -> None:
        """Save current session to file."""
        try:
            session_data = {
                "memory": self.memory.model_dump(),
                "context": self.context,
                "config": self.config.model_dump()
            }
            
            import json
            with open(session_path, 'w') as f:
                json.dump(session_data, f, indent=2, default=str)
                
            self.log_info(f"Session saved to {session_path}")
            
        except Exception as e:
            self.log_error(f"Error saving session: {e}")
    
    def load_session(self, session_path: Path) -> None:
        """Load session from file."""
        try:
            import json
            with open(session_path, 'r') as f:
                session_data = json.load(f)
            
            # Restore memory
            if "memory" in session_data:
                self.memory = Memory(**session_data["memory"])
            
            # Restore context
            if "context" in session_data:
                self.context.update(session_data["context"])
                
            self.log_info(f"Session loaded from {session_path}")
            
        except Exception as e:
            self.log_error(f"Error loading session: {e}")
