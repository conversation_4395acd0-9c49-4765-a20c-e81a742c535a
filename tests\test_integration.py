"""
Integration tests for the Augment Coding Assistant.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch

from augment_assistant.core.config import Config
from augment_assistant.core.agent import AugmentCodingAssistant
from augment_assistant.core.base import TaskResult


class TestIntegration:
    """Integration tests for the complete system."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        config = Config()
        config.debug = True
        config.ai.api_key = "test-key"  # Mock API key
        return config
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.mark.asyncio
    async def test_assistant_initialization(self, config):
        """Test assistant initialization."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            assert assistant.config == config
            assert len(assistant.capabilities) > 0
            assert "filesystem" in assistant.capabilities
            assert "terminal" in assistant.capabilities
            assert "web" in assistant.capabilities
            assert "code_analysis" in assistant.capabilities
    
    @pytest.mark.asyncio
    async def test_file_operations_workflow(self, config, temp_dir):
        """Test complete file operations workflow."""
        config.working_directory = temp_dir
        
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Test file creation through AI request
            with patch.object(assistant.gemini_client, 'generate_response') as mock_generate:
                mock_generate.return_value = TaskResult(
                    success=True,
                    data="THINK: I need to create a file\nACT: filesystem:write_file:test.txt|Hello World\nFINAL: File created successfully"
                )
                
                result = await assistant.process_request("Create a file called test.txt with content 'Hello World'")
                
                # The reasoning engine should have processed this
                assert result.success is True
    
    @pytest.mark.asyncio
    async def test_code_analysis_workflow(self, config, temp_dir):
        """Test code analysis workflow."""
        config.working_directory = temp_dir
        
        # Create a Python file to analyze
        test_file = temp_dir / "test_code.py"
        test_code = '''
def hello_world():
    """Print hello world."""
    print("Hello, World!")

class TestClass:
    """A test class."""
    
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"
'''
        test_file.write_text(test_code)
        
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Get code analysis capability
            code_agent = assistant.get_capability("code_analysis")
            
            # Test AST parsing
            result = await code_agent.execute("parse_ast", parameters=str(test_file))
            
            assert result.success is True
            assert "functions" in result.data
            assert "classes" in result.data
            assert len(result.data["functions"]) == 1
            assert len(result.data["classes"]) == 1
            assert result.data["functions"][0]["name"] == "hello_world"
            assert result.data["classes"][0]["name"] == "TestClass"
    
    @pytest.mark.asyncio
    async def test_terminal_operations(self, config):
        """Test terminal operations."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Get terminal capability
            terminal_agent = assistant.get_capability("terminal")
            
            # Test simple command execution
            result = await terminal_agent.execute("execute_command", parameters="echo 'Hello Terminal'")
            
            assert result.success is True
            assert "Hello Terminal" in result.data["stdout"]
            assert result.data["return_code"] == 0
    
    @pytest.mark.asyncio
    async def test_capability_listing(self, config):
        """Test capability listing and information."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Test listing capabilities
            capabilities = assistant.list_capabilities()
            
            expected_capabilities = ["filesystem", "terminal", "web", "code_analysis"]
            for cap in expected_capabilities:
                assert cap in capabilities
            
            # Test getting specific capability
            fs_capability = assistant.get_capability("filesystem")
            assert fs_capability is not None
            assert fs_capability.name == "filesystem"
            assert "file system" in fs_capability.description.lower()
    
    @pytest.mark.asyncio
    async def test_status_and_context(self, config):
        """Test status reporting and context management."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Test status
            status = assistant.get_status()
            
            assert "capabilities" in status
            assert "memory_entries" in status
            assert "config" in status
            assert status["capabilities"] > 0
            
            # Test context management
            assistant.update_context("test_key", "test_value")
            assert assistant.get_context("test_key") == "test_value"
            assert assistant.get_context("nonexistent", "default") == "default"
    
    @pytest.mark.asyncio
    async def test_memory_system(self, config):
        """Test memory system functionality."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Test memory operations
            assistant.memory.store_short_term("recent_task", "file_operation")
            assistant.memory.store_long_term("user_preference", "python_style")
            
            assert assistant.memory.retrieve("recent_task") == "file_operation"
            assert assistant.memory.retrieve("user_preference") == "python_style"
            
            # Test conversation history
            assistant.memory.add_to_conversation("user", "Hello assistant")
            assistant.memory.add_to_conversation("assistant", "Hello! How can I help?")
            
            history = assistant.memory.get_recent_conversation(2)
            assert len(history) == 2
            assert history[0]["role"] == "user"
            assert history[1]["role"] == "assistant"
    
    @pytest.mark.asyncio
    async def test_session_save_load(self, config, temp_dir):
        """Test session save and load functionality."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Add some data to the session
            assistant.update_context("test_context", "test_value")
            assistant.memory.store_short_term("test_memory", "test_data")
            
            # Save session
            session_file = temp_dir / "test_session.json"
            assistant.save_session(session_file)
            
            assert session_file.exists()
            
            # Create new assistant and load session
            assistant2 = AugmentCodingAssistant(config)
            assistant2.load_session(session_file)
            
            # Verify data was loaded
            assert assistant2.get_context("test_context") == "test_value"
            assert assistant2.memory.retrieve("test_memory") == "test_data"
    
    @pytest.mark.asyncio
    async def test_error_handling(self, config):
        """Test error handling throughout the system."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Test invalid capability
            invalid_cap = assistant.get_capability("nonexistent")
            assert invalid_cap is None
            
            # Test filesystem error handling
            fs_agent = assistant.get_capability("filesystem")
            result = await fs_agent.execute("read_file", parameters="/nonexistent/file.txt")
            assert result.success is False
            assert "not found" in result.error.lower()
            
            # Test terminal error handling
            terminal_agent = assistant.get_capability("terminal")
            result = await terminal_agent.execute("execute_command", parameters="nonexistent_command_xyz")
            assert result.success is False
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, config, temp_dir):
        """Test concurrent operations."""
        config.working_directory = temp_dir
        
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Create multiple files concurrently
            fs_agent = assistant.get_capability("filesystem")
            
            async def create_file(name, content):
                return await fs_agent.execute("write_file", parameters=f"{temp_dir}/{name}|{content}")
            
            # Run concurrent file operations
            tasks = [
                create_file("file1.txt", "content1"),
                create_file("file2.txt", "content2"),
                create_file("file3.txt", "content3")
            ]
            
            results = await asyncio.gather(*tasks)
            
            # All operations should succeed
            for result in results:
                assert result.success is True
            
            # Verify files were created
            assert (temp_dir / "file1.txt").exists()
            assert (temp_dir / "file2.txt").exists()
            assert (temp_dir / "file3.txt").exists()
    
    @pytest.mark.asyncio
    async def test_shutdown_cleanup(self, config):
        """Test proper shutdown and cleanup."""
        with patch('augment_assistant.ai.gemini_client.genai.configure'):
            assistant = AugmentCodingAssistant(config)
            
            # Start some operations
            fs_agent = assistant.get_capability("filesystem")
            terminal_agent = assistant.get_capability("terminal")
            
            # Shutdown should complete without errors
            await assistant.shutdown()
            
            # Verify capabilities are properly cleaned up
            # (This is mainly to ensure no exceptions are raised)
            assert True  # If we get here, shutdown was successful


if __name__ == "__main__":
    pytest.main([__file__])
