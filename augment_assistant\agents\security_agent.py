"""
Security Agent - Specialized in security analysis and vulnerability detection.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class SecurityAgent(BaseAgent):
    """Specialized agent for security analysis."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("SecurityAgent", [])
        self.gemini_client = gemini_client
    
    async def analyze_security(self, code: str) -> TaskResult:
        """Analyze code for security vulnerabilities."""
        prompt = f"""
        As a senior security expert, analyze this code for vulnerabilities:
        
        Code: {code}
        
        Provide:
        1. Security vulnerability assessment
        2. OWASP Top 10 compliance
        3. Secure coding recommendations
        4. Threat modeling insights
        5. Remediation strategies
        """
        
        return await self.gemini_client.generate_response(prompt)
