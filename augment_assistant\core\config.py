"""
Configuration management for the Augment Coding Assistant.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv
import yaml
import json

# Load environment variables
load_dotenv()


class AIConfig(BaseModel):
    """AI model configuration."""
    provider: str = Field(default="gemini", description="AI provider (gemini, openai, etc.)")
    model: str = Field(default="gemini-2.0-flash-lite", description="Model name")
    api_key: Optional[str] = Field(default=None, description="API key")
    temperature: float = Field(default=0.7, description="Temperature for generation")
    max_tokens: int = Field(default=8192, description="Maximum tokens")
    timeout: int = Field(default=30, description="Request timeout in seconds")


class TerminalConfig(BaseModel):
    """Terminal interface configuration."""
    theme: str = Field(default="dark", description="Terminal theme")
    show_progress: bool = Field(default=True, description="Show progress bars")
    auto_scroll: bool = Field(default=True, description="Auto scroll output")
    max_history: int = Field(default=1000, description="Maximum command history")


class FileSystemConfig(BaseModel):
    """File system operations configuration."""
    watch_directories: list = Field(default_factory=list, description="Directories to watch")
    ignore_patterns: list = Field(default_factory=lambda: [".git", "__pycache__", "*.pyc"], description="Patterns to ignore")
    backup_enabled: bool = Field(default=True, description="Enable file backups")
    max_file_size: int = Field(default=10*1024*1024, description="Maximum file size to process (bytes)")


class WebConfig(BaseModel):
    """Web scraping configuration."""
    user_agent: str = Field(default="AugmentCodingAssistant/1.0", description="User agent string")
    timeout: int = Field(default=30, description="Request timeout")
    max_retries: int = Field(default=3, description="Maximum retries")
    delay_between_requests: float = Field(default=1.0, description="Delay between requests")


class Config(BaseModel):
    """Main configuration class."""
    
    # Core settings
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    working_directory: Path = Field(default_factory=lambda: Path.cwd(), description="Working directory")
    
    # Component configurations
    ai: AIConfig = Field(default_factory=AIConfig)
    terminal: TerminalConfig = Field(default_factory=TerminalConfig)
    filesystem: FileSystemConfig = Field(default_factory=FileSystemConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    
    # Advanced settings
    enable_reasoning: bool = Field(default=True, description="Enable AI reasoning")
    enable_planning: bool = Field(default=True, description="Enable task planning")
    enable_memory: bool = Field(default=True, description="Enable conversation memory")
    max_context_length: int = Field(default=32000, description="Maximum context length")
    
    @classmethod
    def load_from_file(cls, config_path: Path) -> "Config":
        """Load configuration from file."""
        if not config_path.exists():
            return cls()
            
        with open(config_path, 'r') as f:
            if config_path.suffix == '.yaml' or config_path.suffix == '.yml':
                data = yaml.safe_load(f)
            elif config_path.suffix == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {config_path.suffix}")
                
        return cls(**data)
    
    @classmethod
    def load_from_env(cls) -> "Config":
        """Load configuration from environment variables."""
        config = cls()
        
        # AI configuration
        if os.getenv("GEMINI_API_KEY"):
            config.ai.api_key = os.getenv("GEMINI_API_KEY")
        if os.getenv("AI_MODEL"):
            config.ai.model = os.getenv("AI_MODEL")
        if os.getenv("AI_TEMPERATURE"):
            config.ai.temperature = float(os.getenv("AI_TEMPERATURE"))
            
        # Debug mode
        if os.getenv("DEBUG"):
            config.debug = os.getenv("DEBUG").lower() == "true"
            
        # Log level
        if os.getenv("LOG_LEVEL"):
            config.log_level = os.getenv("LOG_LEVEL")
            
        return config
    
    def save_to_file(self, config_path: Path) -> None:
        """Save configuration to file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            if config_path.suffix == '.yaml' or config_path.suffix == '.yml':
                yaml.dump(self.model_dump(), f, default_flow_style=False)
            elif config_path.suffix == '.json':
                json.dump(self.model_dump(), f, indent=2)
            else:
                raise ValueError(f"Unsupported config file format: {config_path.suffix}")
    
    def get_config_dir(self) -> Path:
        """Get configuration directory."""
        config_dir = Path.home() / ".augment_assistant"
        config_dir.mkdir(exist_ok=True)
        return config_dir
    
    def get_cache_dir(self) -> Path:
        """Get cache directory."""
        cache_dir = self.get_config_dir() / "cache"
        cache_dir.mkdir(exist_ok=True)
        return cache_dir
    
    def get_logs_dir(self) -> Path:
        """Get logs directory."""
        logs_dir = self.get_config_dir() / "logs"
        logs_dir.mkdir(exist_ok=True)
        return logs_dir
