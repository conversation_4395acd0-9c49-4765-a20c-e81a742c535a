"""
Core components of the Augment Coding Assistant.

This module contains the fundamental building blocks:
- Main agent controller
- Configuration management
- Base classes and interfaces
"""

from .agent import AugmentCodingAssistant
from .config import Config
from .base import BaseAgent, BaseCapability

__all__ = [
    "AugmentCodingAssistant",
    "Config", 
    "BaseAgent",
    "BaseCapability"
]
