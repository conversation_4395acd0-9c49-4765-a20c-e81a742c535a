"""
Multi-Modal Interface - Revolutionary visual and audio capabilities.

This module provides multi-modal capabilities that surpass all current 2025 coding assistants
by supporting visual, audio, and text interactions simultaneously.

Features:
- Screenshot analysis and UI/UX understanding
- Voice commands and audio explanations
- Diagram and flowchart generation
- Visual code representation
- Audio feedback and narration
- Multi-modal reasoning and understanding
- Visual debugging and error explanation
- Interactive visual programming
"""

from .visual_processor import VisualProcessor
from .audio_processor import AudioProcessor
from .diagram_generator import DiagramGenerator
from .multimodal_reasoner import MultiModalReasoner
from .visual_debugger import VisualDebugger

__all__ = [
    "VisualProcessor",
    "AudioProcessor",
    "DiagramGenerator",
    "MultiModalReasoner",
    "VisualDebugger"
]
