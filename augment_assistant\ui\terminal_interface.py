"""
Rich terminal interface for the Augment Coding Assistant.
"""

import asyncio
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.tree import Tree
from rich.prompt import Prompt, Confirm
from rich.layout import Layout
from rich.live import Live
from rich.align import Align
from rich.columns import Columns
from rich.status import Status
import click

from ..core.config import TerminalConfig
from ..core.base import TaskResult


class TerminalInterface:
    """
    Beautiful terminal interface using Rich with interactive features.
    """
    
    def __init__(self, config: TerminalConfig, agent):
        self.config = config
        self.agent = agent
        self.console = Console(theme=config.theme if hasattr(config, 'theme') else None)
        self.command_history = []
        self.current_session = None
        self.is_running = False
        
        # UI Components
        self.layout = Layout()
        self.progress = None
        self.status_panel = None
        
        # Command mappings
        self.commands = {
            'help': self._show_help,
            'status': self._show_status,
            'capabilities': self._show_capabilities,
            'history': self._show_history,
            'clear': self._clear_screen,
            'exit': self._exit,
            'quit': self._exit,
            'save': self._save_session,
            'load': self._load_session,
            'config': self._show_config,
            'debug': self._toggle_debug,
        }
    
    async def start_interactive_session(self) -> None:
        """Start the interactive terminal session."""
        self.is_running = True
        
        # Show welcome message
        self._show_welcome()
        
        try:
            while self.is_running:
                try:
                    # Get user input
                    user_input = await self._get_user_input()
                    
                    if not user_input.strip():
                        continue
                    
                    # Add to history
                    self.command_history.append({
                        "input": user_input,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    # Process command
                    await self._process_input(user_input)
                    
                except KeyboardInterrupt:
                    if Confirm.ask("\n[yellow]Do you want to exit?[/yellow]"):
                        break
                    else:
                        self.console.print("[green]Continuing...[/green]")
                except EOFError:
                    break
                except Exception as e:
                    self.console.print(f"[red]Error: {e}[/red]")
        
        finally:
            self.is_running = False
            self.console.print("\n[blue]Goodbye! 👋[/blue]")
    
    def _show_welcome(self) -> None:
        """Show welcome message."""
        welcome_panel = Panel(
            Align.center(
                Text.assemble(
                    ("🚀 ", "bold blue"),
                    ("Augment Coding Assistant", "bold white"),
                    (" 🚀", "bold blue"),
                    "\n\n",
                    ("Your AI-powered development companion", "italic cyan"),
                    "\n\n",
                    ("Type ", "white"),
                    ("'help'", "bold green"),
                    (" for available commands", "white")
                )
            ),
            title="[bold blue]Welcome[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(welcome_panel)
        self.console.print()
    
    async def _get_user_input(self) -> str:
        """Get user input with rich prompt."""
        prompt_text = Text()
        prompt_text.append("augment", style="bold blue")
        prompt_text.append(" > ", style="white")
        
        # Use asyncio to make input non-blocking
        loop = asyncio.get_event_loop()
        user_input = await loop.run_in_executor(
            None, 
            lambda: Prompt.ask(prompt_text, console=self.console)
        )
        
        return user_input
    
    async def _process_input(self, user_input: str) -> None:
        """Process user input."""
        user_input = user_input.strip()
        
        # Check if it's a built-in command
        command_parts = user_input.split()
        command = command_parts[0].lower()
        
        if command in self.commands:
            await self.commands[command](command_parts[1:] if len(command_parts) > 1 else [])
        else:
            # Process as AI request
            await self._process_ai_request(user_input)
    
    async def _process_ai_request(self, request: str) -> None:
        """Process AI request with progress indication."""
        with Status("[bold green]Processing your request...", console=self.console) as status:
            try:
                # Process the request through the agent
                result = await self.agent.process_request(request)
                
                status.stop()
                
                if result.success:
                    self._display_result(result)
                else:
                    self.console.print(f"[red]Error: {result.error}[/red]")
                    
            except Exception as e:
                status.stop()
                self.console.print(f"[red]Error processing request: {e}[/red]")
    
    def _display_result(self, result: TaskResult) -> None:
        """Display task result in a formatted way."""
        if isinstance(result.data, str):
            # Simple text result
            if len(result.data) > 1000:
                # Large text - show in a panel with scrolling
                self.console.print(Panel(
                    result.data[:1000] + "\n... (truncated)",
                    title="[bold green]Result[/bold green]",
                    border_style="green"
                ))
            else:
                self.console.print(Panel(
                    result.data,
                    title="[bold green]Result[/bold green]",
                    border_style="green"
                ))
        
        elif isinstance(result.data, dict):
            # Dictionary result - show as formatted JSON or table
            self._display_dict_result(result.data)
        
        elif isinstance(result.data, list):
            # List result - show as table or columns
            self._display_list_result(result.data)
        
        else:
            # Other types - convert to string
            self.console.print(Panel(
                str(result.data),
                title="[bold green]Result[/bold green]",
                border_style="green"
            ))
        
        # Show metadata if available
        if result.metadata:
            self._display_metadata(result.metadata)
    
    def _display_dict_result(self, data: Dict[str, Any]) -> None:
        """Display dictionary result."""
        if len(data) <= 10:  # Small dict - show as table
            table = Table(title="Result", show_header=True, header_style="bold magenta")
            table.add_column("Key", style="cyan")
            table.add_column("Value", style="white")
            
            for key, value in data.items():
                table.add_row(str(key), str(value)[:100] + "..." if len(str(value)) > 100 else str(value))
            
            self.console.print(table)
        else:  # Large dict - show as JSON
            import json
            json_text = json.dumps(data, indent=2, default=str)
            syntax = Syntax(json_text, "json", theme="monokai", line_numbers=True)
            self.console.print(Panel(syntax, title="[bold green]Result (JSON)[/bold green]"))
    
    def _display_list_result(self, data: List[Any]) -> None:
        """Display list result."""
        if not data:
            self.console.print("[yellow]Empty result[/yellow]")
            return
        
        if len(data) <= 20 and all(isinstance(item, (str, int, float)) for item in data):
            # Simple list - show as columns
            columns = Columns(data, equal=True, expand=True)
            self.console.print(Panel(columns, title="[bold green]Result[/bold green]"))
        else:
            # Complex list - show as numbered items
            for i, item in enumerate(data[:50], 1):  # Limit to 50 items
                self.console.print(f"[cyan]{i}.[/cyan] {str(item)[:200]}{'...' if len(str(item)) > 200 else ''}")
            
            if len(data) > 50:
                self.console.print(f"[yellow]... and {len(data) - 50} more items[/yellow]")
    
    def _display_metadata(self, metadata: Dict[str, Any]) -> None:
        """Display metadata information."""
        if metadata:
            meta_text = " | ".join([f"{k}: {v}" for k, v in metadata.items()])
            self.console.print(f"[dim]{meta_text}[/dim]")
    
    async def _show_help(self, args: List[str]) -> None:
        """Show help information."""
        help_table = Table(title="Available Commands", show_header=True, header_style="bold magenta")
        help_table.add_column("Command", style="cyan", width=15)
        help_table.add_column("Description", style="white")
        
        # Built-in commands
        commands_help = {
            "help": "Show this help message",
            "status": "Show assistant status",
            "capabilities": "List available capabilities",
            "history": "Show command history",
            "clear": "Clear the screen",
            "save": "Save current session",
            "load": "Load a session",
            "config": "Show configuration",
            "debug": "Toggle debug mode",
            "exit/quit": "Exit the assistant"
        }
        
        for cmd, desc in commands_help.items():
            help_table.add_row(cmd, desc)
        
        self.console.print(help_table)
        
        # Show capabilities
        self.console.print("\n[bold yellow]Available Capabilities:[/bold yellow]")
        capabilities = self.agent.list_capabilities()
        for cap in capabilities:
            capability = self.agent.get_capability(cap)
            self.console.print(f"  [cyan]•[/cyan] [bold]{cap}[/bold]: {capability.description}")
        
        self.console.print("\n[dim]You can ask me anything about coding, file operations, web scraping, or terminal commands![/dim]")
    
    async def _show_status(self, args: List[str]) -> None:
        """Show assistant status."""
        status_info = self.agent.get_status()
        
        status_table = Table(title="Assistant Status", show_header=True, header_style="bold green")
        status_table.add_column("Property", style="cyan")
        status_table.add_column("Value", style="white")
        
        status_table.add_row("Capabilities", str(status_info.get("capabilities", 0)))
        status_table.add_row("Memory Entries", str(status_info.get("memory_entries", 0)))
        status_table.add_row("AI Model", status_info.get("config", {}).get("ai_model", "Unknown"))
        status_table.add_row("Debug Mode", str(status_info.get("config", {}).get("debug_mode", False)))
        status_table.add_row("Working Directory", str(status_info.get("config", {}).get("working_directory", "Unknown")))
        status_table.add_row("Session Commands", str(len(self.command_history)))
        
        self.console.print(status_table)
    
    async def _show_capabilities(self, args: List[str]) -> None:
        """Show detailed capabilities information."""
        capabilities_tree = Tree("[bold blue]Available Capabilities[/bold blue]")
        
        for cap_name in self.agent.list_capabilities():
            capability = self.agent.get_capability(cap_name)
            cap_branch = capabilities_tree.add(f"[bold cyan]{cap_name}[/bold cyan]")
            cap_branch.add(f"[dim]{capability.description}[/dim]")
            
            # Show available actions
            actions = capability.get_available_actions()
            if actions:
                actions_branch = cap_branch.add("[yellow]Actions:[/yellow]")
                for action in actions:
                    actions_branch.add(f"[green]• {action}[/green]")
        
        self.console.print(capabilities_tree)
    
    async def _show_history(self, args: List[str]) -> None:
        """Show command history."""
        if not self.command_history:
            self.console.print("[yellow]No command history available[/yellow]")
            return
        
        history_table = Table(title="Command History", show_header=True, header_style="bold blue")
        history_table.add_column("#", style="cyan", width=5)
        history_table.add_column("Time", style="green", width=20)
        history_table.add_column("Command", style="white")
        
        for i, entry in enumerate(self.command_history[-20:], 1):  # Show last 20
            timestamp = datetime.fromisoformat(entry["timestamp"]).strftime("%H:%M:%S")
            command = entry["input"][:80] + "..." if len(entry["input"]) > 80 else entry["input"]
            history_table.add_row(str(i), timestamp, command)
        
        self.console.print(history_table)
    
    async def _clear_screen(self, args: List[str]) -> None:
        """Clear the screen."""
        self.console.clear()
        self._show_welcome()
    
    async def _exit(self, args: List[str]) -> None:
        """Exit the assistant."""
        self.is_running = False
    
    async def _save_session(self, args: List[str]) -> None:
        """Save current session."""
        filename = args[0] if args else f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        session_path = Path(filename)
        
        try:
            self.agent.save_session(session_path)
            self.console.print(f"[green]Session saved to: {session_path}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving session: {e}[/red]")
    
    async def _load_session(self, args: List[str]) -> None:
        """Load a session."""
        if not args:
            self.console.print("[red]Please specify a session file[/red]")
            return
        
        session_path = Path(args[0])
        
        try:
            self.agent.load_session(session_path)
            self.console.print(f"[green]Session loaded from: {session_path}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error loading session: {e}[/red]")
    
    async def _show_config(self, args: List[str]) -> None:
        """Show configuration."""
        config_dict = self.agent.config.model_dump()
        
        import json
        config_json = json.dumps(config_dict, indent=2, default=str)
        syntax = Syntax(config_json, "json", theme="monokai", line_numbers=True)
        self.console.print(Panel(syntax, title="[bold blue]Configuration[/bold blue]"))
    
    async def _toggle_debug(self, args: List[str]) -> None:
        """Toggle debug mode."""
        self.agent.config.debug = not self.agent.config.debug
        status = "enabled" if self.agent.config.debug else "disabled"
        self.console.print(f"[yellow]Debug mode {status}[/yellow]")
    
    def display_progress(self, task_name: str, total: int = 100) -> Progress:
        """Create and return a progress bar."""
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        )
        
        task_id = progress.add_task(task_name, total=total)
        return progress, task_id
    
    def display_syntax(self, code: str, language: str = "python") -> None:
        """Display syntax-highlighted code."""
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        self.console.print(Panel(syntax, title=f"[bold green]{language.title()} Code[/bold green]"))
    
    def display_markdown(self, markdown_text: str) -> None:
        """Display markdown content."""
        md = Markdown(markdown_text)
        self.console.print(Panel(md, title="[bold blue]Markdown[/bold blue]"))
    
    def display_error(self, error_message: str) -> None:
        """Display error message."""
        self.console.print(Panel(
            f"[red]{error_message}[/red]",
            title="[bold red]Error[/bold red]",
            border_style="red"
        ))
    
    def display_success(self, success_message: str) -> None:
        """Display success message."""
        self.console.print(Panel(
            f"[green]{success_message}[/green]",
            title="[bold green]Success[/bold green]",
            border_style="green"
        ))
