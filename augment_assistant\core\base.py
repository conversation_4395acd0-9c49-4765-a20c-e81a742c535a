"""
Base classes and interfaces for the Augment Coding Assistant.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel
import logging
from rich.console import Console
from rich.logging import RichHandler

# Setup logging with Rich
logging.basicConfig(
    level="INFO",
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)


class TaskResult(BaseModel):
    """Result of a task execution."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = {}


class BaseCapability(ABC):
    """Base class for all capabilities."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"capability.{name}")
        self.console = Console()
    
    @abstractmethod
    async def execute(self, task: str, **kwargs) -> TaskResult:
        """Execute a task using this capability."""
        pass
    
    @abstractmethod
    def get_available_actions(self) -> List[str]:
        """Get list of available actions for this capability."""
        pass
    
    def log_info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def log_error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(message)
    
    def log_debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)


class BaseAgent(ABC):
    """Base class for all agents."""
    
    def __init__(self, name: str, capabilities: List[BaseCapability]):
        self.name = name
        self.capabilities = {cap.name: cap for cap in capabilities}
        self.logger = logging.getLogger(f"agent.{name}")
        self.console = Console()
        self.context = {}
    
    @abstractmethod
    async def process_request(self, request: str, **kwargs) -> TaskResult:
        """Process a user request."""
        pass
    
    def add_capability(self, capability: BaseCapability) -> None:
        """Add a new capability to this agent."""
        self.capabilities[capability.name] = capability
        self.log_info(f"Added capability: {capability.name}")
    
    def remove_capability(self, capability_name: str) -> None:
        """Remove a capability from this agent."""
        if capability_name in self.capabilities:
            del self.capabilities[capability_name]
            self.log_info(f"Removed capability: {capability_name}")
    
    def get_capability(self, name: str) -> Optional[BaseCapability]:
        """Get a capability by name."""
        return self.capabilities.get(name)
    
    def list_capabilities(self) -> List[str]:
        """List all available capabilities."""
        return list(self.capabilities.keys())
    
    def update_context(self, key: str, value: Any) -> None:
        """Update agent context."""
        self.context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get value from agent context."""
        return self.context.get(key, default)
    
    def log_info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def log_error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(message)
    
    def log_debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)


class ReasoningStep(BaseModel):
    """A single step in the reasoning process."""
    step_number: int
    thought: str
    action: Optional[str] = None
    observation: Optional[str] = None
    confidence: float = 1.0


class ReasoningChain(BaseModel):
    """A chain of reasoning steps."""
    goal: str
    steps: List[ReasoningStep] = []
    final_answer: Optional[str] = None
    success: bool = False
    
    def add_step(self, thought: str, action: str = None, observation: str = None, confidence: float = 1.0) -> None:
        """Add a reasoning step."""
        step = ReasoningStep(
            step_number=len(self.steps) + 1,
            thought=thought,
            action=action,
            observation=observation,
            confidence=confidence
        )
        self.steps.append(step)
    
    def get_current_step(self) -> Optional[ReasoningStep]:
        """Get the current (last) reasoning step."""
        return self.steps[-1] if self.steps else None
    
    def complete(self, final_answer: str, success: bool = True) -> None:
        """Mark the reasoning chain as complete."""
        self.final_answer = final_answer
        self.success = success


class Memory(BaseModel):
    """Memory system for storing and retrieving information."""
    short_term: Dict[str, Any] = {}
    long_term: Dict[str, Any] = {}
    conversation_history: List[Dict[str, Any]] = []
    
    def store_short_term(self, key: str, value: Any) -> None:
        """Store information in short-term memory."""
        self.short_term[key] = value
    
    def store_long_term(self, key: str, value: Any) -> None:
        """Store information in long-term memory."""
        self.long_term[key] = value
    
    def retrieve(self, key: str, memory_type: str = "both") -> Any:
        """Retrieve information from memory."""
        if memory_type == "short_term":
            return self.short_term.get(key)
        elif memory_type == "long_term":
            return self.long_term.get(key)
        else:  # both
            return self.short_term.get(key) or self.long_term.get(key)
    
    def add_to_conversation(self, role: str, content: str, metadata: Dict[str, Any] = None) -> None:
        """Add entry to conversation history."""
        entry = {
            "role": role,
            "content": content,
            "timestamp": None,  # Will be set by the calling code
            "metadata": metadata or {}
        }
        self.conversation_history.append(entry)
    
    def get_recent_conversation(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        return self.conversation_history[-limit:] if self.conversation_history else []
