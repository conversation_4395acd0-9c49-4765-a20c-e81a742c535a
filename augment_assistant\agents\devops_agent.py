"""
DevOps Agent - Specialized in deployment and infrastructure.
"""

from ..core.base import BaseAgent, TaskResult
from ..ai.gemini_client import GeminiClient


class DevOpsAgent(BaseAgent):
    """Specialized agent for DevOps and infrastructure."""
    
    def __init__(self, gemini_client: GeminiClient):
        super().__init__("DevOpsAgent", [])
        self.gemini_client = gemini_client
    
    async def plan_deployment(self, application: str, environment: str = "production") -> TaskResult:
        """Plan deployment strategy."""
        prompt = f"""
        As a senior DevOps engineer, plan deployment for:
        
        Application: {application}
        Environment: {environment}
        
        Provide:
        1. Deployment strategy
        2. Infrastructure requirements
        3. CI/CD pipeline design
        4. Monitoring and alerting
        5. Rollback procedures
        """
        
        return await self.gemini_client.generate_response(prompt)
